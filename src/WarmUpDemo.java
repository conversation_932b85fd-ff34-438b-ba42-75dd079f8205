public class WarmUpDemo {
    public static void main(String[] args) {
        int N = 100;
        double[][] A = new double[N][N];
        double[][] B = new double[N][N];
        double[][] C = new double[N][N];

        // Khởi tạo dữ liệu
        for (int i = 0; i < N; i++) {
            for (int j = 0; j < N; j++) {
                A[i][j] = 1.0;
                B[i][j] = 2.0;
            }
        }

        int iterations = 10; // chạy 10 lần lớn
        int innerLoops = 100_000; // mỗi lần chạy 100k vòng
        long[] times = new long[iterations];

        for (int t = 0; t < iterations; t++) {
            long start = System.nanoTime();

            for (int loop = 0; loop < innerLoops; loop++) {
                for (int i = 0; i < N; i++) {
                    for (int k = 0; k < N; k++) {
                        double aik = A[i][k];
                        for (int j = 0; j < N; j++) {
                            C[i][j] += aik * B[k][j];
                        }
                    }
                }
            }

            long end = System.nanoTime();
            times[t] = (end - start) / 1_000_000; // đ<PERSON><PERSON> sang ms
            System.out.printf("Lần %d: %d ms%n", t + 1, times[t]);
        }

        System.out.println("==== So sánh ====");
        System.out.printf("Lần đầu: %d ms%n", times[0]);
        System.out.printf("Lần cuối: %d ms%n", times[iterations - 1]);
    }
}
