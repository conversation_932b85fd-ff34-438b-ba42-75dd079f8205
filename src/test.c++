#include <iostream>
#include <vector>
#include <chrono>

int main() {
    const int N = 100;
    std::vector<std::vector<double>> A(N, std::vector<double>(N, 1.0));
    std::vector<std::vector<double>> B(N, std::vector<double>(N, 2.0));
    std::vector<std::vector<double>> C(N, std::vector<double>(N, 0.0));

    int iterations = 10;       // số lần lớn
    int innerLoops = 100000;   // mỗi lần chạy 100k vòng
    std::vector<long long> times(iterations);

    for (int t = 0; t < iterations; t++) {
        auto start = std::chrono::high_resolution_clock::now();

        for (int loop = 0; loop < innerLoops; loop++) {
            for (int i = 0; i < N; i++) {
                for (int k = 0; k < N; k++) {
                    double aik = A[i][k];
                    for (int j = 0; j < N; j++) {
                        C[i][j] += aik * B[k][j];
                    }
                }
            }
        }

        auto end = std::chrono::high_resolution_clock::now();
        times[t] = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
        std::cout << "Lần " << (t + 1) << ": " << times[t] << " ms" << std::endl;
    }

    std::cout << "==== So sánh ====" << std::endl;
    std::cout << "Lần đầu: " << times[0] << " ms" << std::endl;
    std::cout << "Lần cuối: " << times[iterations - 1] << " ms" << std::endl;

    return 0;
}
