package com.example.demo.entity;

import com.example.demo.entity.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

@Data
@Entity
@Table(name = "ket_luan_thanh_tra")
public class KetLuanThanhTraEntity extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "seq_kltt")
    @SequenceGenerator(name = "seq_kltt", sequenceName = "Seq_KetLuanThanhTra", allocationSize = 1)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "MA_KLTT", nullable = false, length = 50)
    private String maKltt;

    @Column(name = "KHU_VUC", nullable = false, length = 100)
    private String khuVuc;

    @Column(name = "MA_CHI_NHANH_I", nullable = false, length = 50)
    private String maChiNhanhI;

    @Column(name = "MA_CHI_NHANH_II", nullable = false, length = 50)
    private String maChiNhanhII;

    @Column(name = "NGAY_KET_LUAN")
    @Temporal(TemporalType.DATE)
    private Date ngayKetLuan;

    @Column(name = "ID_CBXL", nullable = false)
    private Integer canBoXuLyId;
}
