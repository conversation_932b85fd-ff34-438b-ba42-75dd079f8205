package com.example.demo.entity;

import com.example.demo.entity.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Data;

@Entity
@Table(name = "sai_pham")
@Data
public class SaiPhamEntity extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "seq_saipham")
    @SequenceGenerator(name = "seq_saipham", sequenceName = "Seq_SaiPham", allocationSize = 1)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "KIENNGHI_ID")
    private Integer kienNghiId;

    @Column(name = "NOI_DUNG_SAI_PHAM", nullable = false)
    private String noiDungSaiPham;

    @Column(name = "TINH_HINH_SAI_PHAM", nullable = false, length = 255)
    private String tinhHinhSaiPham;

    @Column(name = "MA_THANH_TRA", nullable = false, length = 50)
    private String maThanhTra;
}
