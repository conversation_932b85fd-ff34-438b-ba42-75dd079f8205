package com.example.demo.entity;

import com.example.demo.entity.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Data;

@Entity
@Table(name = "khach_hang")
@Data
public class KhachHangEntity extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "seq_khachhang")
    @SequenceGenerator(name = "seq_khachhang", sequenceName = "Seq_KhachHang", allocationSize = 1)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "NAME", nullable = false, length = 100)
    private String name;
}
