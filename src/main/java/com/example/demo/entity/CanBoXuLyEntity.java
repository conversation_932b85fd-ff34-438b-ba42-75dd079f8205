package com.example.demo.entity;

import com.example.demo.entity.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Data;

@Entity
@Table(name = "can_bo_xu_ly")
@Data
public class CanBoXuLyEntity extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "seq_canboxuly")
    @SequenceGenerator(name = "seq_canboxuly", sequenceName = "Seq_CanBoXuLy", allocationSize = 1)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "NAME", nullable = false, length = 100)
    private String name;
}
