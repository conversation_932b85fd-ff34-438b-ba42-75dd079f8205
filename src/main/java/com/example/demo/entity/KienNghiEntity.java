package com.example.demo.entity;

import com.example.demo.entity.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

@Entity
@Table(name = "kien_nghi")
@Data
public class KienNghiEntity extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "seq_kiennghi")
    @SequenceGenerator(name = "seq_kiennghi", sequenceName = "Seq_KienNghi", allocationSize = 1)
    @Column(name = "ID")
    private Integer id;
    @Column(name = "KETLUANTHANHTRA_ID")
    private Integer ketLuanThanhTraId;

    @Column(name = "NOI_DUNG_KIEN_NGHI", nullable = false)
    private String noiDungKienNghi;

    @Column(name = "LOAI", nullable = false, length = 50)
    private String loai;

    @Column(name = "KHU_VUC", nullable = false, length = 100)
    private String khuVuc;

    @Column(name = "NGAY_KHAC_PHUC", nullable = false)
    @Temporal(TemporalType.DATE)
    private Date ngayKhacPhuc;
}
