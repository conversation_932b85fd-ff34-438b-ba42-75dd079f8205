package com.example.demo.service.impl;

import com.example.demo.dto.PaginationRequest;
import com.example.demo.entity.CanBoXuLyEntity;
import com.example.demo.repository.CanBoXuLyRepository;
import com.example.demo.service.CanBoXuLyService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CanBoXuLyServiceImpl implements CanBoXuLyService {
    CanBoXuLyRepository canBoXuLyRepository;

    @Override
    public Page<CanBoXuLyEntity> findAll(PaginationRequest paginationRequest) {
        return canBoXuLyRepository.findAll(paginationRequest);
    }
}
