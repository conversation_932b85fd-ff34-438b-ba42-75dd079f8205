package com.example.demo.service.impl;

import com.example.demo.dto.PaginationRequest;
import com.example.demo.dto.request.KetLuanThanhTraResquest;
import com.example.demo.entity.KetLuanThanhTraEntity;
import com.example.demo.exception.ApplicationException;
import com.example.demo.exception.ErrorCode;
import com.example.demo.repository.KetLuanThanhTraRepository;
import com.example.demo.service.KetLuanThanhTraService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class KetLuanThanhTraServiceImpl implements KetLuanThanhTraService {
    KetLuanThanhTraRepository repository;
    private final KetLuanThanhTraRepository ketLuanThanhTraRepository;

    /**
     * Tạo mới hoặc cập nhật một đối tượng KetLuanThanhTra.
     *
     * @param ketLuanThanhTra đối tượng cần tạo hoặc cập nhật.
     * @return đối tượng KetLuanThanhTra đã được lưu.
     */
    @Override
    public KetLuanThanhTraEntity createOrUpdate(KetLuanThanhTraEntity ketLuanThanhTra) {
        if (ketLuanThanhTra.getId() == null) {
            // Validate dữ liệu khi tạo mới
            ketLuanThanhTra.setNgayKetLuan(new Date());
            if (repository.existsByMaKltt(ketLuanThanhTra.getMaKltt())) {
                throw new ApplicationException(ErrorCode.KET_LUAN_ALREADY_EXISTS,
                        "Kết luận thanh tra với tên '" + ketLuanThanhTra.getMaKltt() + "' đã tồn tại.");
            }
        } else {
            // Validate dữ liệu khi cập nhật
            Optional<KetLuanThanhTraEntity> existingEntity = repository.findById(ketLuanThanhTra.getId());
            if (existingEntity.isEmpty()) {
                throw new ApplicationException(ErrorCode.KET_LUAN_NOT_FOUND,
                        "Không tìm thấy kết luận thanh tra với ID: " + ketLuanThanhTra.getId());
            }
        }
        return repository.save(ketLuanThanhTra);
    }

    /**
     * Xóa một đối tượng KetLuanThanhTra theo ID.
     *
     * @param id ID của đối tượng cần xóa.
     */
    @Override
    public void delete(Integer id) {
        if (!repository.existsById(id)) {
            throw new ApplicationException(ErrorCode.KET_LUAN_NOT_FOUND,
                    "Không tìm thấy kết luận thanh tra với ID: " + id);
        }
        repository.deleteById(id);
    }

    /**
     * Lấy danh sách tất cả các đối tượng KetLuanThanhTra.
     *
     * @return danh sách các đối tượng.
     */
    @Override
    public Page<KetLuanThanhTraResquest> findAll(PaginationRequest paginationRequest) {
        return ketLuanThanhTraRepository.findAll(paginationRequest);
    }

    /**
     * Tìm một đối tượng KetLuanThanhTra theo ID.
     *
     * @param id ID của đối tượng cần tìm.
     * @return một Optional chứa đối tượng nếu tìm thấy, hoặc rỗng nếu không tìm thấy.
     */
    @Override
    public KetLuanThanhTraEntity findById(Integer id) throws ApplicationException {
        return repository.findById(id).orElseThrow(() -> new ApplicationException(ErrorCode.KET_LUAN_NOT_FOUND,
                "Không tìm thấy kết luận thanh tra với ID: " + id));
    }
}
