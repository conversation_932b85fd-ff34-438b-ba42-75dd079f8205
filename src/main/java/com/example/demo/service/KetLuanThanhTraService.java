package com.example.demo.service;

import com.example.demo.dto.PaginationRequest;
import com.example.demo.dto.request.KetLuanThanhTraResquest;
import com.example.demo.entity.KetLuanThanhTraEntity;
import com.example.demo.exception.ApplicationException;
import org.springframework.data.domain.Page;

public interface KetLuanThanhTraService {

    /**
     * Tạo mới hoặc cập nhật một đối tượng KetLuanThanhTra.
     *
     * @param ketLuanThanhTra đối tượng cần tạo hoặc cập nhật.
     * @return đối tượng KetLuanThanhTra đã được lưu.
     */
    KetLuanThanhTraEntity createOrUpdate(KetLuanThanhTraEntity ketLuanThanhTra);

    /**
     * Xóa một đối tượng KetLuanThanhTra theo ID.
     *
     * @param id ID của đối tượng cần xóa.
     */
    void delete(Integer id);

    /**
     * L<PERSON>y danh sách tất cả các đối tượng KetLuanThanhTra.
     *
     * @return danh sách các đối tượng.
     */
    Page<KetLuanThanhTraResquest> findAll(PaginationRequest paginationRequest);

    /**
     * Tìm một đối tượng KetLuanThanhTra theo ID.
     *
     * @param id ID của đối tượng cần tìm.
     * @return một Optional chứa đối tượng nếu tìm thấy, hoặc rỗng nếu không tìm thấy.
     */
    KetLuanThanhTraEntity findById(Integer id) throws ApplicationException;
}
