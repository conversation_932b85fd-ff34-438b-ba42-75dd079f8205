package com.example.demo.service;

import com.example.demo.entity.UserEntity;
import com.example.demo.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class UserService {
    private final UserRepository userRepository;
    public List<UserEntity> findAll() {
        return userRepository.findAll();
    }
    public UserEntity save(UserEntity userEntity) {
        System.out.println(userEntity);
        return userRepository.save(userEntity);
    }
}
