package com.example.demo.dto;

public class GraphSubscriptionRequest {
    private String changeType;
    private String notificationUrl;
    private String resource;
    private String expirationDateTime;
    private String clientState;
    private Boolean includeResourceData; // Optional

    // Getters and Setters
    public String getChangeType() {
        return changeType;
    }

    public void setChangeType(String changeType) {
        this.changeType = changeType;
    }

    public String getNotificationUrl() {
        return notificationUrl;
    }

    public void setNotificationUrl(String notificationUrl) {
        this.notificationUrl = notificationUrl;
    }

    public String getResource() {
        return resource;
    }

    public void setResource(String resource) {
        this.resource = resource;
    }

    public String getExpirationDateTime() {
        return expirationDateTime;
    }

    public void setExpirationDateTime(String expirationDateTime) {
        this.expirationDateTime = expirationDateTime;
    }

    public String getClientState() {
        return clientState;
    }

    public void setClientState(String clientState) {
        this.clientState = clientState;
    }

    public Boolean getIncludeResourceData() {
        return includeResourceData;
    }

    public void setIncludeResourceData(Boolean includeResourceData) {
        this.includeResourceData = includeResourceData;
    }
}