package com.example.demo.dto.request;


import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
public class KetLuanThanhTraResquest {
    String maKltt;
    String khuVuc;
    String maChiNhanhI;
    String maChiNhanhII;
    String ngayKetLuan;
    String canBoXuLyName;
    Integer canBoXuLyId;
}
