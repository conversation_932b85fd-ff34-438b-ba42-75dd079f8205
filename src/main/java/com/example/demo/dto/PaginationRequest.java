package com.example.demo.dto;


import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

public class PaginationRequest {

    @NotNull(message = "Page number cannot be null")
    @Min(value = 0, message = "Page number must be greater than or equal to 0")
    private Integer page;

    @NotNull(message = "Page size cannot be null")
    @Min(value = 1, message = "Page size must be greater than or equal to 1")
    private Integer size;

    private String sortBy = "id"; // Default sortBy column
    private String sortType = "asc"; // Default sort direction (asc or desc)

    // Getters and Setters
    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public String getSortBy() {
        return sortBy;
    }

    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }

    public String getSortType() {
        return sortType;
    }

    public void setSortType(String sortType) {
        this.sortType = sortType;
    }

    @Override
    public String toString() {
        return "PaginationRequest{" +
                "page=" + page +
                ", size=" + size +
                ", sortBy='" + sortBy + '\'' +
                ", sortType='" + sortType + '\'' +
                '}';
    }

    public Pageable toPageable() {
        Sort sort = sortBy != null
                ? Sort.by(Sort.Direction.fromString(sortType != null ? sortType : "ASC"), sortBy)
                : Sort.unsorted();
        return PageRequest.of(page, size, sort);
    }
}
