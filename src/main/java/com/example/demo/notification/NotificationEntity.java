package com.example.demo.notification;

import com.example.demo.entity.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "notifications")
@Getter
@Setter
public class NotificationEntity extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String userId; // Assuming userId is a String, adjust if needed
    private String message;
    private boolean readStatus;

    // Getters and setters are generated by Lombok
}