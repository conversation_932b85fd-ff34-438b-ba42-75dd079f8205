package com.example.demo.notification;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

@Service
public class NotificationService {

    private final SimpMessagingTemplate messagingTemplate;
    private final NotificationRepository notificationRepository;

    @Autowired
    public NotificationService(SimpMessagingTemplate messagingTemplate, NotificationRepository notificationRepository) {
        this.messagingTemplate = messagingTemplate;
        this.notificationRepository = notificationRepository;
    }

    public void sendNotificationToUser(String userId, String message) {
        // Save notification to database
        NotificationEntity notification = new NotificationEntity();
        notification.setUserId(userId);
        notification.setMessage(message);
        notification.setReadStatus(false);
        notificationRepository.save(notification);

        // Send notification via WebSocket
        // The destination will be /queue/notifications/{userId}
        messagingTemplate.convertAndSendToUser(userId, "/queue/notifications", notification);
    }

    // TODO: Implement or call the actual getUserName() method from your auth service
    private String getUserName() {
        // Placeholder for getting the current authenticated user's name
        // This should be replaced with the actual implementation using your Keycloak service
        return "currentAuthenticatedUser"; // Replace with actual user ID
    }
}