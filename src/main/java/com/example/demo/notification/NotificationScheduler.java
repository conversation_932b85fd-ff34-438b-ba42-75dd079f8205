package com.example.demo.notification;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class NotificationScheduler {

    private final NotificationService notificationService;

    @Autowired
    public NotificationScheduler(NotificationService notificationService) {
        this.notificationService = notificationService;
    }

    // Schedule this task to run every 10 seconds
    @Scheduled(fixedRate = 10000)
    public void sendScheduledNotification() {
        // TODO: Get the actual user ID dynamically
        String userId = getUserName(); // Using the placeholder getUserName()

        String message = "Scheduled notification: " + System.currentTimeMillis();
        notificationService.sendNotificationToUser(userId, message);
    }

    // TODO: Implement or call the actual getUserName() method from your auth service
    private String getUserName() {
        // Placeholder for getting the current authenticated user's name
        // This should be replaced with the actual implementation using your Keycloak service
        return "currentAuthenticatedUser"; // Replace with actual user ID
    }
}