package com.example.demo.exception;

import org.springframework.http.HttpStatus;

public enum ErrorCode {
    // Common Errors
    RESOURCE_NOT_FOUND("ERR001", "Resource not found", HttpStatus.NOT_FOUND),
    INVALID_INPUT("ERR002", "Invalid input provided", HttpStatus.BAD_REQUEST),
    INTERNAL_SERVER_ERROR("ERR003", "Internal server error", HttpStatus.INTERNAL_SERVER_ERROR),
    PERMISSION_DENIED("ERR004", "Permission denied", HttpStatus.FORBIDDEN),

    // KetLuanThanhTra Specific Errors
    KET_LUAN_NOT_FOUND("KLTT001", "Kết luận thanh tra không tồn tại", HttpStatus.NOT_FOUND),
    KET_LUAN_ALREADY_EXISTS("KLTT002", "<PERSON>ết luận thanh tra đã tồn tại", HttpStatus.BAD_REQUEST),
    INVALID_KET_LUAN_DATA("KLTT003", "<PERSON><PERSON> liệu kết luận thanh tra không hợp lệ", HttpStatus.BAD_REQUEST),
    KET_LUAN_UPDATE_FAILED("KLTT004", "Cập nhật kết luận thanh tra thất bại", HttpStatus.INTERNAL_SERVER_ERROR),
    KET_LUAN_DELETE_FAILED("KLTT005", "Xóa kết luận thanh tra thất bại", HttpStatus.INTERNAL_SERVER_ERROR),
    KET_LUAN_CREATION_FAILED("KLTT006", "Tạo mới kết luận thanh tra thất bại", HttpStatus.INTERNAL_SERVER_ERROR);

    private final String code;
    private final String message;
    private final HttpStatus httpStatus;

    ErrorCode(String code, String message, HttpStatus httpStatus) {
        this.code = code;
        this.message = message;
        this.httpStatus = httpStatus;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public HttpStatus getHttpStatus() {
        return httpStatus;
    }
}
