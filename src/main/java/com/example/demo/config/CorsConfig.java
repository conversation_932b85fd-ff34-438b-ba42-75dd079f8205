package com.example.demo.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class CorsConfig {

    @Bean
    public WebMvcConfigurer corsConfigurer() {
        return new WebMvcConfigurer() {
            @Override
            public void addCorsMappings(CorsRegistry registry) {
                registry.addMapping("/**") // Cho phép tất cả các endpoint
                        .allowedOrigins("http://localhost:8889") // Domain frontend
                        .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS") // C<PERSON>c phương thức được phép
                        .allowedHeaders("*") // Các header được phép
                        .allowCredentials(true); // <PERSON><PERSON><PERSON> c<PERSON> gửi cookie hoặc header xác thực
            }
        };
    }
}
