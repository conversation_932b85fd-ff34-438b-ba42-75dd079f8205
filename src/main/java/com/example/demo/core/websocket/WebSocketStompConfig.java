package com.example.demo.core.websocket;// src/main/java/com/example/websocketdemo/config/WebSocketStompConfig.java

import com.example.demo.config.AuthHandshakeInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;

@Configuration
@EnableWebSocketMessageBroker
public class WebSocketStompConfig implements WebSocketMessageBrokerConfigurer {

    @Override
    public void configureMessageBroker(MessageBrokerRegistry registry) {
        // Dùng Simple Broker: đủ cho các ứng dụng không yêu cầu clustering hay persistence
        registry.enableSimpleBroker("/topic", "/queue");
        // Nếu muốn sử dụng Broker Relay (ví dụ với RabbitMQ), uncomment phần dưới và comment enableSimpleBroker
        /*
        registry.enableStompBrokerRelay("/topic", "/queue")
                .setRelayHost("your-broker-host")
                .setRelayPort(61613)
                .setClientLogin("your-login")
                .setClientPasscode("your-passcode");
        */
        registry.setApplicationDestinationPrefixes("/app");
    }

    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        registry.addEndpoint("/ws")
                .addInterceptors(new AuthHandshakeInterceptor())
                .setAllowedOrigins("*");
        // Nếu sử dụng SockJS:
        // registry.addEndpoint("/ws").addInterceptors(new AuthHandshakeInterceptor()).setAllowedOrigins("*").withSockJS();
    }
}