package com.example.demo.core.response;

import com.example.demo.exception.ErrorCode;
import org.springframework.http.HttpStatus;

public class ResponseUtils {
    public static <T> ResponseResult<T> success(T result) {
        ResponseResult<T> responseResult = new ResponseResult<>();
        responseResult.setCode("200");
        responseResult.setResult(result);
        responseResult.setStatus(HttpStatus.OK);
        return responseResult;
    }

    public static <T> ResponseResult<T> error(T result, ErrorCode errorCode) {
        ResponseResult<T> responseResult = new ResponseResult<>();
        responseResult.setCode(errorCode.getCode());
        responseResult.setMessage(errorCode.getMessage());
        responseResult.setResult(result);
        return responseResult;
    }
}
