package com.example.demo.controller;

import com.example.demo.core.response.ResponseResult;
import com.example.demo.core.response.ResponseUtils;
import com.example.demo.dto.PaginationRequest;
import com.example.demo.entity.CanBoXuLyEntity;
import com.example.demo.service.CanBoXuLyService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/can-bo-xu-ly")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CanBoXuLyController {
    CanBoXuLyService canBoXuLyService;

    /**
     * <PERSON><PERSON>y danh sách tất cả các cán bộ xử lý.
     *
     * @return danh sách các đối tượng CanBoXuLy.
     */
    @GetMapping
    public ResponseResult<Page<CanBoXuLyEntity>> findAll() {
        //logic socket real-time when call api
        String message = "Dữ liệu cán bộ xử lý đã được tải thành công!";
        // Find all CanBoXuLy
        return ResponseUtils.success(canBoXuLyService.findAll(new PaginationRequest()));
    }

}
