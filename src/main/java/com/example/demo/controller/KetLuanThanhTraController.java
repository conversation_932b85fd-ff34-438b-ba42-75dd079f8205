package com.example.demo.controller;

import com.example.demo.core.response.ResponseResult;
import com.example.demo.core.response.ResponseUtils;
import com.example.demo.dto.PaginationRequest;
import com.example.demo.dto.request.KetLuanThanhTraResquest;
import com.example.demo.entity.KetLuanThanhTraEntity;
import com.example.demo.exception.ApplicationException;
import com.example.demo.service.KetLuanThanhTraService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/ket-luan-thanh-tra")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class KetLuanThanhTraController {

    KetLuanThanhTraService ketLuanThanhTraService;

    /**
     * <PERSON><PERSON><PERSON> danh sách tất cả các kết luận thanh tra.
     *
     * @return danh sách các đối tượng KetLuanThanhTra.
     */
    @GetMapping
    public ResponseResult<Page<KetLuanThanhTraResquest>> findAll(@Validated PaginationRequest paginationRequest) {
        return ResponseUtils.success(ketLuanThanhTraService.findAll(paginationRequest));
    }

    /**
     * Lấy thông tin một kết luận thanh tra theo ID.
     *
     * @param id ID của kết luận thanh tra.
     * @return đối tượng KetLuanThanhTra nếu tìm thấy.
     */
    @GetMapping("/{id}")
    public ResponseResult<KetLuanThanhTraEntity> findById(@PathVariable Integer id) throws ApplicationException {
        KetLuanThanhTraEntity ketLuanThanhTra = ketLuanThanhTraService.findById(id);
        return ResponseUtils.success(ketLuanThanhTra);
    }

    /**
     * Tạo mới hoặc cập nhật một kết luận thanh tra.
     *
     * @param ketLuanThanhTra đối tượng cần tạo hoặc cập nhật.
     * @return đối tượng KetLuanThanhTra đã được lưu.
     */
    @PostMapping
    public ResponseResult<KetLuanThanhTraEntity> createOrUpdate(@RequestBody KetLuanThanhTraEntity ketLuanThanhTra) {
        KetLuanThanhTraEntity savedEntity = ketLuanThanhTraService.createOrUpdate(ketLuanThanhTra);
        return ResponseUtils.success(savedEntity);
    }

    /**
     * Xóa một kết luận thanh tra theo ID.
     *
     * @param id ID của kết luận thanh tra cần xóa.
     * @return phản hồi trạng thái.
     */
    @DeleteMapping("/{id}")
    public ResponseResult<String> delete(@PathVariable Integer id) {
        //use virtual thread with 1000000 task

        return ResponseUtils.success("OK");
    }

}
