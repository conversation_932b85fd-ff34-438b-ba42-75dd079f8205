package com.example.demo.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.microsoft.bot.connector.authentication.*;
import com.microsoft.bot.schema.Activity;
import com.microsoft.bot.schema.ActivityTypes;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/messages") // Endpoint mà Bot Framework sẽ gọi đến
public class BotController {

    private static final Logger logger = LoggerFactory.getLogger(BotController.class);
    private final ObjectMapper objectMapper;
    private final ChannelProvider channelProvider; // Thường là SimpleChannelProvider

    @Value("${microsoft.bot.app-id}")
    private String microsoftAppId;

    @Value("${microsoft.bot.app-password}")
    private String microsoftAppPassword;

    public BotController() {
        this.objectMapper = new ObjectMapper()
                .enable(SerializationFeature.INDENT_OUTPUT)
                .findAndRegisterModules(); // Quan trọng để xử lý các kiểu dữ liệu như ngày tháng
        this.channelProvider = new SimpleChannelProvider(); // Sử dụng SimpleChannelProvider mặc định
    }

    @PostMapping
    public ResponseEntity<Void> handleBotActivity(@RequestBody Activity activity, HttpServletRequest httpRequest) {
        // 1. Log Headers (để debug, đặc biệt là Authorization header)
        logHeaders(httpRequest);

        // 2. Log toàn bộ Activity nhận được (dưới dạng JSON)
        try {
            logger.info("Received Activity from Azure Bot: \n{}", objectMapper.writeValueAsString(activity));
        } catch (JsonProcessingException e) {
            logger.error("Error serializing activity to JSON for logging", e);
        }

        // 3. XÁC THỰC YÊU CẦU (CỰC KỲ QUAN TRỌNG)
        SimpleCredentialProvider credentialProvider = new SimpleCredentialProvider(microsoftAppId, microsoftAppPassword);
        try {
            String authHeader = httpRequest.getHeader(HttpHeaders.AUTHORIZATION);
            if (authHeader == null || authHeader.isEmpty()) {
                logger.warn("UNAUTHORIZED: Missing Authorization header.");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build(); // Trả về 401 không có body
            }
            // Tham số cuối của authenticateRequest có thể là một ClaimsValidator hoặc null nếu không cần custom validation
            JwtTokenValidation.authenticateRequest(activity, authHeader, credentialProvider, this.channelProvider, null).join();
            logger.info("Request authenticated successfully!");

        } catch (AuthenticationException e) {
            logger.error("AUTHENTICATION FAILED: {}", e.getMessage());
            // Ghi log chi tiết lỗi hơn nếu cần, ví dụ e.getLocalizedMessage() hoặc stack trace ở debug level
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build(); // Trả về 401 không có body
        } catch (Exception e) { // Bắt các lỗi khác từ CompletableFuture.join() hoặc quá trình xác thực
            logger.error("ERROR DURING AUTHENTICATION: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }

        // 4. Xử lý Activity sau khi đã xác thực thành công
        try {
            if (ActivityTypes.MESSAGE.equals(activity.getType())) {
                // Xử lý tin nhắn từ người dùng
                handleMessage(activity);
            } else if (ActivityTypes.CONVERSATION_UPDATE.equals(activity.getType())) {
                // Xử lý khi bot được thêm vào/xóa khỏi chat, hoặc thành viên thay đổi
                logger.info("Received ConversationUpdate Activity. MembersAdded: {}, MembersRemoved: {}",
                        activity.getMembersAdded() != null ? activity.getMembersAdded().size() : 0,
                        activity.getMembersRemoved() != null ? activity.getMembersRemoved().size() : 0);
                // Có thể thêm logic chào mừng ở đây nếu muốn
            } else {
                logger.info("Received Activity of type: {}. Not handled in this simple controller.", activity.getType());
            }
        } catch (Exception e) {
            logger.error("Error processing activity after authentication: {}", e.getMessage(), e);
            // Cân nhắc trả về lỗi 500 cho bot biết có vấn đề phía server
            // return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }


        // 5. Luôn trả về HTTP 200 OK để Bot Framework biết đã nhận và xử lý (hoặc đang xử lý)
        // Ngay cả khi có lỗi bên trong logic xử lý (sau xác thực), việc trả 200 OK
        // ngăn Bot Framework cố gắng gửi lại Activity (retry).
        // Lỗi nội bộ nên được log và xử lý riêng.
        return ResponseEntity.ok().build();
    }

    private void handleMessage(Activity messageActivity) {
        // A. Xử lý dữ liệu từ Adaptive Card Action.Submit
        if (messageActivity.getValue() != null) {
            try {
                JsonNode cardActionValue = objectMapper.convertValue(messageActivity.getValue(), JsonNode.class);
                logger.info("Adaptive Card Action.Submit Data: {}", objectMapper.writeValueAsString(cardActionValue));

                if (cardActionValue.has("action")) {
                    String actionType = cardActionValue.get("action").asText();
                    String alertId = cardActionValue.has("alertId") ? cardActionValue.get("alertId").asText() : "N/A";

                    if ("confirm".equals(actionType)) {
                        logger.info("USER ACTION: Confirmed alertId: {} by User: {}", alertId, messageActivity.getFrom().getName());
                        // TODO: Gọi service của bạn để xử lý confirm alertId
                        // Ví dụ: myAlertService.confirmAlert(alertId, messageActivity.getFrom().getId());
                        System.out.println("ACTION: Confirmed alertId: " + alertId); // Tạm thời in ra console
                    } else if ("reject".equals(actionType)) {
                        logger.info("USER ACTION: Rejected alertId: {} by User: {}", alertId, messageActivity.getFrom().getName());
                        // TODO: Gọi service của bạn để xử lý reject alertId
                        // Ví dụ: myAlertService.rejectAlert(alertId, messageActivity.getFrom().getId());
                        System.out.println("ACTION: Rejected alertId: " + alertId); // Tạm thời in ra console
                    } else {
                        logger.warn("Unknown card action received: {}", actionType);
                    }
                }
            } catch (Exception e) {
                logger.error("Error processing Adaptive Card value: {}", e.getMessage(), e);
            }
        }
        // B. Xử lý tin nhắn văn bản (lệnh)
        else if (messageActivity.getText() != null && !messageActivity.getText().trim().isEmpty()) {
            String commandText = messageActivity.getText().trim();
            // Đơn giản hóa: không xóa @mention, chỉ kiểm tra lệnh
            logger.info("Received text message from {}: '{}'", messageActivity.getFrom().getName(), commandText);

            if (commandText.equalsIgnoreCase("hello")) {
                logger.info("COMMAND: Received 'hello' command from {}", messageActivity.getFrom().getName());
                System.out.println("COMMAND: Hello received from " + messageActivity.getFrom().getName());
                // TODO: Có thể gửi tin nhắn trả lời nếu bạn triển khai logic đó
            } else if (commandText.toLowerCase().startsWith("get alert")) {
                logger.info("COMMAND: Received 'get alert' command: {}", commandText);
                System.out.println("COMMAND: Get alert command: " + commandText);
                // TODO: Parse alert ID và xử lý
            } else {
                logger.info("Received unhandled text message: {}", commandText);
            }
        }
    }

    private void logHeaders(HttpServletRequest httpRequest) {
        try {
            Map<String, String> headersMap = new HashMap<>();
            Enumeration<String> headerNames = httpRequest.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String key = headerNames.nextElement();
                headersMap.put(key, httpRequest.getHeader(key));
            }
            logger.info("Received Headers: \n{}", objectMapper.writeValueAsString(headersMap));
        } catch (JsonProcessingException e) {
            logger.error("Error serializing headers to JSON for logging", e);
        }
    }
}