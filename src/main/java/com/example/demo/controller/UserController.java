package com.example.demo.controller;

import com.example.demo.entity.UserEntity;
import com.example.demo.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/")
@RequiredArgsConstructor
public class UserController {
    private final UserService userService;
    @GetMapping
    public UserEntity findUsername(Long id){
        var userEntity = new UserEntity();
        userEntity.setId(id);
        userEntity.setUsername("Bao");
        userEntity.setPassword("123456");
        userEntity.setEmail("<EMAIL>");
        return userEntity;
    }
    @GetMapping("/all")
    public List<UserEntity> findAll(){
        return userService.findAll();
    }
    @PostMapping
    public UserEntity save(@RequestBody UserEntity userEntity){
        return userService.save(userEntity);
    }
}
