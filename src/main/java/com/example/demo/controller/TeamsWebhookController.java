package com.example.demo.controller;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Slf4j
@RestController
@RequestMapping("/api/teams/webhook")
public class TeamsWebhookController {

    @PostMapping
    public ResponseEntity<String> receiveWebhook(HttpServletRequest request, @RequestParam(name = "validationToken", required = false) String validationToken) {
        try {
            // Handle Microsoft validation token
            if (validationToken != null && !validationToken.isEmpty()) {
                log.info("Received validationToken: {}", validationToken);
                return ResponseEntity.ok()
                        .header("Content-Type", "text/plain")
                        .body(validationToken);
            }

            // Read and log request body
            String requestBody = StreamUtils.copyToString(request.getInputStream(), StandardCharsets.UTF_8);
            log.info("Received change notification: {}", requestBody);

            // TODO: Parse JSON and process notification

            return ResponseEntity.ok().build();
        } catch (IOException e) {
            log.error("Error reading webhook payload", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Invalid request payload");
        }
    }

    @GetMapping("/api/teams/webhook")
    public ResponseEntity<String> handleValidation(@RequestParam(name = "validationToken", required = false) String validationToken) {
        if (validationToken != null) {
            return ResponseEntity
                    .ok()
                    .header(HttpHeaders.CONTENT_TYPE, "text/plain")
                    .body(validationToken);
        } else {
            return ResponseEntity.badRequest().body("Missing validationToken");
        }
    }

}
