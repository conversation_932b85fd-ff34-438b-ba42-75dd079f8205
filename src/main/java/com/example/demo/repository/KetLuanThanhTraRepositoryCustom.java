package com.example.demo.repository;

import com.example.demo.dto.PaginationRequest;
import com.example.demo.dto.request.KetLuanThanhTraResquest;
import org.springframework.data.domain.Page;

public interface KetLuanThanhTraRepositoryCustom {
    /* find all ket luan thanh tra by pagination
    @param paginationRequest
    @return page of ket luan thanh tra
    */
    Page<KetLuanThanhTraResquest> findAll(PaginationRequest paginationRequest);
}
