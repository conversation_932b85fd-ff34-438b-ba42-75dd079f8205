package com.example.demo.repository;

import com.example.demo.dto.PaginationRequest;
import com.example.demo.entity.CanBoXuLyEntity;
import org.springframework.data.domain.Page;

public interface CanBoXuLyRepositoryCustom {
    /* find all ket luan thanh tra by pagination
    @param paginationRequest
    @return page of ket luan thanh tra
    */
    Page<CanBoXuLyEntity> findAll(PaginationRequest paginationRequest);
}
