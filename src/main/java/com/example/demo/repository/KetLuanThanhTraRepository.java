package com.example.demo.repository;

import com.example.demo.entity.KetLuanThanhTraEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface KetLuanThanhTraRepository extends JpaRepository<KetLuanThanhTraEntity, Integer>, KetLuanThanhTraRepositoryCustom {
    /*
        exists by maKltt
        @param maKltt ma ket luan thanh tra
        @return boolean
     */
    boolean existsByMaKltt(String maKltt);
}
