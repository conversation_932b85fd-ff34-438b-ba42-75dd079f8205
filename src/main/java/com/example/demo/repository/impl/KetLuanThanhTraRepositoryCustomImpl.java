package com.example.demo.repository.impl;

import com.example.demo.dto.PaginationRequest;
import com.example.demo.dto.request.KetLuanThanhTraResquest;
import com.example.demo.repository.KetLuanThanhTraRepositoryCustom;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.text.SimpleDateFormat;
import java.util.List;

@RequiredArgsConstructor
@FieldDefaults(level = lombok.AccessLevel.PRIVATE, makeFinal = true)
public class KetLuanThanhTraRepositoryCustomImpl implements KetLuanThanhTraRepositoryCustom {

    @PersistenceContext
    EntityManager entityManager;

    @Override
    public Page<KetLuanThanhTraResquest> findAll(PaginationRequest paginationRequest) {
        Pageable pageable = paginationRequest.toPageable();

        // Truy vấn chính
        String baseQuery = """
                    SELECT 
                        kl.MA_KLTT AS maKltt, 
                        kl.KHU_VUC AS khuVuc, 
                        kl.MA_CHI_NHANH_I AS maChiNhanhI, 
                        kl.MA_CHI_NHANH_II AS maChiNhanhII, 
                        kl.NGAY_KET_LUAN AS ngayKetLuan, 
                        cb.NAME AS canBoXuLyName, 
                        cb.ID AS canBoXuLyId
                    FROM 
                        ket_luan_thanh_tra kl
                    LEFT JOIN 
                        can_bo_xu_ly cb ON kl.id_cbxl = cb.id
                """;

        // Truy vấn đếm tổng số bản ghi
        String countQuery = """
                    SELECT COUNT(*)
                    FROM ket_luan_thanh_tra kl
                    LEFT JOIN can_bo_xu_ly cb ON kl.id_cbxl = cb.id
                """;

        // Sort
        String sortClause = paginationRequest.getSortBy() != null
                ? " ORDER BY kl." + paginationRequest.getSortBy() + " " + paginationRequest.getSortType()
                : " ORDER BY kl.NGAY_KET_LUAN DESC";

        // Native query
        Query query = entityManager.createNativeQuery(baseQuery + sortClause);
        Query countQueryTyped = entityManager.createNativeQuery(countQuery);

        // Pagination
        query.setFirstResult((int) pageable.getOffset());
        query.setMaxResults(pageable.getPageSize());

        // Map kết quả trả về
        List<KetLuanThanhTraResquest> results = query.getResultList()
                .stream()
                .map(record -> {
                    Object[] row = (Object[]) record;
                    java.sql.Date sqlDate = (java.sql.Date) row[4];  // Lấy ngày kết luận
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    String ngayKetLuan = sdf.format(sqlDate); // Chuyển Date thành String

                    return new KetLuanThanhTraResquest(
                            (String) row[0],  // maKltt
                            (String) row[1],  // khuVuc
                            (String) row[2],  // maChiNhanhI
                            (String) row[3],  // maChiNhanhII
                            ngayKetLuan,      // ngayKetLuan
                            (String) row[5],  // canBoXuLyName
                            (Integer) row[6]   // canBoXuLyId
                    );
                })
                .toList();


        // Tổng số bản ghi
        long total = ((Number) countQueryTyped.getSingleResult()).longValue();

        return new PageImpl<>(results, pageable, total);
    }

}
