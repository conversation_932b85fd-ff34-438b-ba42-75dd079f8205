package com.example.demo.repository.impl;

import com.example.demo.dto.PaginationRequest;
import com.example.demo.entity.CanBoXuLyEntity;
import com.example.demo.repository.CanBoXuLyRepositoryCustom;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.util.List;

@RequiredArgsConstructor
@FieldDefaults(level = lombok.AccessLevel.PRIVATE, makeFinal = true)
public class CanBoXuLyRepositoryCustomImpl implements CanBoXuLyRepositoryCustom {

    @PersistenceContext
    EntityManager entityManager;

    @Override
    public Page<CanBoXuLyEntity> findAll(PaginationRequest paginationRequest) {
        // Create Pageable from PaginationRequest
        Pageable pageable = paginationRequest.toPageable();
        // Write native SQL queries
        String baseQuery = "SELECT * FROM can_bo_xu_ly c";
        String countQuery = "SELECT COUNT(*) FROM can_bo_xu_ly c";

        // Sort
        String sortClause = paginationRequest.getSortBy() != null
                ? " ORDER BY c." + paginationRequest.getSortBy() + " " + paginationRequest.getSortType()
                : "";

        // Create native queries
        Query query = entityManager.createNativeQuery(baseQuery + sortClause, CanBoXuLyEntity.class);
        Query countQueryTyped = entityManager.createNativeQuery(countQuery);

        // Pagination
        query.setFirstResult((int) pageable.getOffset());
        query.setMaxResults(pageable.getPageSize());

        // Results
        List<CanBoXuLyEntity> results = query.getResultList();
        long total = ((Number) countQueryTyped.getSingleResult()).longValue();

        return new PageImpl<>(results, pageable, total);
    }
}