CREATE SEQUENCE Seq_KetLuanThanhTra
    AS INT
    START WITH 1
    INCREMENT BY 1;
CREATE TABLE KetLuanThanhTra (
                                 ID INT PRIMARY KEY, -- <PERSON>h<PERSON><PERSON> chính
                                 MA_KLTT NVARCHAR(50) NOT NULL, -- <PERSON>ã kết luận thanh tra
                                 KHU_VUC NVARCHAR(100) NOT NULL, -- <PERSON><PERSON> vực
                                 MA_CHI_NHANH_I NVARCHAR(50) NOT NULL, -- Mã chi nhánh cấp I
                                 MA_CHI_NHANH_II NVARCHAR(50) NOT NULL, -- Mã chi nhánh cấp II
                                 NGAY_KET_LUAN DATE NOT NULL, -- <PERSON><PERSON><PERSON> kết luận thanh tra
                                 NGAY_KL DATE NOT NULL, -- Ng<PERSON><PERSON> KL
                                 ID_CBXL INT NOT NULL, -- ID cán bộ xử lý
                                 CREATED_DATE DATETIME NOT NULL DEFAULT GETDATE(), -- <PERSON><PERSON><PERSON> tạo
                                 CREATED_USER NVARCHAR(200) NOT NULL, -- <PERSON><PERSON><PERSON><PERSON> tạo
                                 UPDATED_DATE DATETIME NULL, -- <PERSON><PERSON><PERSON> cập nhật
                                 UPDATED_USER NVARCHAR(200) NULL -- Người cập nhật
);
CREATE SEQUENCE Seq_KienNghi
    AS INT
    START WITH 1
    INCREMENT BY 1;
CREATE TABLE KienNghi (
                          ID INT PRIMARY KEY, -- Khóa chính
                          KETLUANTHANHTRA_ID INT NOT NULL, -- ID tham chiếu đến bảng KetLuanThanhTra
                          CREATE_DATE DATETIME NOT NULL DEFAULT GETDATE(), -- Ngày tạo
                          CREATE_USER NVARCHAR(50) NOT NULL, -- Người tạo
                          UPDATE_DATE DATETIME NULL, -- Ngày cập nhật
                          UPDATE_USER NVARCHAR(50) NULL, -- Người cập nhật
                          NOI_DUNG_KIEN_NGHI NVARCHAR(MAX) NOT NULL, -- Nội dung kiến nghị
                          LOAI NVARCHAR(50) NOT NULL, -- Loại kiến nghị
                          KHU_VUC NVARCHAR(100) NOT NULL, -- Khu vực
                          NGAY_KHAC_PHUC DATE NOT NULL, -- Ngày khắc phục
                          CONSTRAINT FK_KetLuanThanhTra FOREIGN KEY (KETLUANTHANHTRA_ID) REFERENCES KetLuanThanhTra(ID) -- Ràng buộc khoá ngoại
);
CREATE SEQUENCE Seq_SaiPham
    AS INT
    START WITH 1
    INCREMENT BY 1;
CREATE TABLE SaiPham (
                         ID INT PRIMARY KEY, -- Khóa chính
                         KIENNGHI_ID INT NOT NULL, -- ID tham chiếu đến bảng KienNghi
                         CREATE_DATE DATETIME NOT NULL DEFAULT GETDATE(), -- Ngày tạo
                         CREATE_USER NVARCHAR(50) NOT NULL, -- Người tạo
                         UPDATE_DATE DATETIME NULL, -- Ngày cập nhật
                         UPDATE_USER NVARCHAR(50) NULL, -- Người cập nhật
                         NOI_DUNG_SAI_PHAM NVARCHAR(MAX) NOT NULL, -- Nội dung sai phạm
                         TINH_HINH_SAI_PHAM NVARCHAR(255) NOT NULL, -- Tình hình sai phạm
                         MA_THANH_TRA NVARCHAR(50) NOT NULL, -- Mã thanh tra
                         CONSTRAINT FK_KienNghi FOREIGN KEY (KIENNGHI_ID) REFERENCES KienNghi(ID) -- Ràng buộc khoá ngoại
);
CREATE SEQUENCE Seq_KhachHang
    AS INT
    START WITH 1
    INCREMENT BY 1;
CREATE TABLE KhachHang (
                           ID INT PRIMARY KEY, -- Khóa chính
                           NAME NVARCHAR(100) NOT NULL, -- Tên khách hàng
                           CREATE_DATE DATETIME NOT NULL DEFAULT GETDATE(), -- Ngày tạo
                           CREATE_USER NVARCHAR(50) NOT NULL, -- Người tạo
                           UPDATE_DATE DATETIME NULL, -- Ngày cập nhật
                           UPDATE_USER NVARCHAR(50) NULL -- Người cập nhật
);
CREATE SEQUENCE Seq_CanBoXuLy
    AS INT
    START WITH 1
    INCREMENT BY 1;
CREATE TABLE CanBoXuLy (
                           ID INT PRIMARY KEY, -- Khóa chính
                           NAME NVARCHAR(100) NOT NULL, -- Tên cán bộ xử lý
                           CREATE_DATE DATETIME NOT NULL DEFAULT GETDATE(), -- Ngày tạo
                           CREATE_USER NVARCHAR(50) NOT NULL, -- Người tạo
                           UPDATE_DATE DATETIME NULL, -- Ngày cập nhật
                           UPDATE_USER NVARCHAR(50) NULL -- Người cập nhật
);

