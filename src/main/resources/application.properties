server.port=9000
spring.datasource.url=*****************************************************************
spring.datasource.username=sa
spring.datasource.password=StrongPassword123!
spring.datasource.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.jpa.database-platform=org.hibernate.dialect.SQLServer2012Dialect
spring.jpa.hibernate.ddl-auto=update
# config kafka
spring.kafka.bootstrap-servers=localhost:9092
spring.kafka.consumer.group-id=group_id
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.enable-auto-commit=false
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer
#config virtual thread
spring.jakartaee.enableVirtualThreads=true
spring.jakartaee.enableVirtualThreadEx
# Microsoft Bot Framework Credentials
microsoft.bot.app-id=43a08ab6-a27c-4714-9b32-0cbbcf27ecb1
microsoft.bot.app-password=YOUR_NEW_SECRET_VALUE_HERE