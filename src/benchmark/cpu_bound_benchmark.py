#!/usr/bin/env python3
"""
Python3 CPU-Bound Benchmark Suite
Equivalent to Java and C++ benchmarks for performance comparison
"""

import time
import random
import math
from typing import List, Tuple

class BenchmarkResult:
    def __init__(self, test_name: str, times: List[float]):
        self.test_name = test_name
        self.times = times
        self.average_time = sum(times) / len(times)
        self.improvement_ratio = times[0] / times[-1] if len(times) > 1 and times[-1] > 0 else 1.0

def matrix_multiplication(iterations: int, matrix_size: int, inner_loops: int) -> BenchmarkResult:
    """Matrix multiplication benchmark"""
    print("=== Matrix Multiplication Benchmark ===")
    print(f"Matrix size: {matrix_size}x{matrix_size}, Inner loops: {inner_loops}")
    
    times = []
    
    for t in range(iterations):
        # Initialize matrices
        A = [[random.random() for _ in range(matrix_size)] for _ in range(matrix_size)]
        B = [[random.random() for _ in range(matrix_size)] for _ in range(matrix_size)]
        C = [[0.0 for _ in range(matrix_size)] for _ in range(matrix_size)]
        
        start_time = time.perf_counter()
        
        for loop in range(inner_loops):
            for i in range(matrix_size):
                for k in range(matrix_size):
                    aik = A[i][k]
                    for j in range(matrix_size):
                        C[i][j] += aik * B[k][j]
        
        end_time = time.perf_counter()
        elapsed_ms = (end_time - start_time) * 1000
        times.append(elapsed_ms)
        print(f"Iteration {t + 1}: {elapsed_ms:.0f} ms")
    
    return BenchmarkResult("Matrix Multiplication", times)

def prime_calculation(iterations: int, max_number: int) -> BenchmarkResult:
    """Prime number calculation benchmark"""
    print("=== Prime Number Calculation Benchmark ===")
    print(f"Finding primes up to: {max_number}")
    
    times = []
    
    def is_prime(n: int) -> bool:
        if n < 2:
            return False
        if n == 2:
            return True
        if n % 2 == 0:
            return False
        
        for i in range(3, int(math.sqrt(n)) + 1, 2):
            if n % i == 0:
                return False
        return True
    
    for t in range(iterations):
        start_time = time.perf_counter()
        
        primes = []
        for num in range(2, max_number + 1):
            if is_prime(num):
                primes.append(num)
        
        end_time = time.perf_counter()
        elapsed_ms = (end_time - start_time) * 1000
        times.append(elapsed_ms)
        print(f"Iteration {t + 1}: {elapsed_ms:.0f} ms (found {len(primes)} primes)")
    
    return BenchmarkResult("Prime Calculation", times)

def mathematical_operations(iterations: int, operation_count: int) -> BenchmarkResult:
    """Mathematical operations benchmark"""
    print("=== Mathematical Operations Benchmark ===")
    print(f"Operations count: {operation_count}")
    
    times = []
    
    for t in range(iterations):
        start_time = time.perf_counter()
        
        result = 0.0
        for i in range(operation_count):
            x = random.random() * 100
            result += math.sin(x) * math.cos(x) + math.sqrt(x) + math.log(x + 1) + math.pow(x, 0.5)
        
        end_time = time.perf_counter()
        elapsed_ms = (end_time - start_time) * 1000
        times.append(elapsed_ms)
        print(f"Iteration {t + 1}: {elapsed_ms:.0f} ms (result: {result:.6f})")
    
    return BenchmarkResult("Mathematical Operations", times)

def fibonacci_calculation(iterations: int, fib_number: int) -> BenchmarkResult:
    """Fibonacci calculation benchmark"""
    print("=== Fibonacci Calculation Benchmark ===")
    print(f"Calculating fibonacci({fib_number})")
    
    times = []
    
    def fibonacci_iterative(n: int) -> int:
        if n <= 1:
            return n
        a, b = 0, 1
        for _ in range(2, n + 1):
            a, b = b, a + b
        return b
    
    for t in range(iterations):
        start_time = time.perf_counter()
        
        result = fibonacci_iterative(fib_number)
        
        end_time = time.perf_counter()
        elapsed_ms = (end_time - start_time) * 1000
        times.append(elapsed_ms)
        print(f"Iteration {t + 1}: {elapsed_ms:.0f} ms (fib({fib_number}) = {result % 10**10})")
    
    return BenchmarkResult("Fibonacci Calculation", times)

def main():
    print("Python3 CPU-Bound Benchmark Suite")
    print("==================================")
    
    results = []
    
    # Run benchmarks (reduced workload for Python)
    results.append(matrix_multiplication(10, 50, 100))  # Smaller matrix, fewer loops
    print()

    results.append(prime_calculation(10, 10000))  # Fewer primes
    print()

    results.append(mathematical_operations(10, 100000))  # Fewer operations
    print()

    results.append(fibonacci_calculation(10, 10000))  # Much smaller fibonacci
    print()
    
    # Print summary
    print("=== SUMMARY ===")
    for result in results:
        print(f"{result.test_name:<25}: First: {result.times[0]:6.0f} ms, "
              f"Last: {result.times[-1]:6.0f} ms, "
              f"Avg: {result.average_time:8.2f} ms, "
              f"Improvement: {result.improvement_ratio:.2f}x")

if __name__ == "__main__":
    main()
