package benchmark;

import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.concurrent.*;

public class IOBoundBenchmark {
    
    public static class BenchmarkResult {
        public String testName;
        public long[] times;
        public double averageTime;
        public double improvementRatio;
        
        public BenchmarkResult(String testName, long[] times) {
            this.testName = testName;
            this.times = times;
            this.averageTime = calculateAverage(times);
            this.improvementRatio = times.length > 1 ? (double)times[0] / times[times.length - 1] : 1.0;
        }
        
        private double calculateAverage(long[] times) {
            long sum = 0;
            for (long time : times) sum += time;
            return (double)sum / times.length;
        }
    }
    
    // Test 1: File Writing and Reading
    public static BenchmarkResult fileOperations(int iterations, int fileCount, int fileSize) {
        System.out.println("=== File Operations Benchmark ===");
        System.out.println("File count: " + fileCount + ", File size: " + fileSize + " bytes");
        
        long[] times = new long[iterations];
        String tempDir = "temp_benchmark";
        
        for (int t = 0; t < iterations; t++) {
            long start = System.nanoTime();
            
            try {
                // Create temp directory
                Path tempPath = Paths.get(tempDir);
                if (!Files.exists(tempPath)) {
                    Files.createDirectory(tempPath);
                }
                
                // Generate test data
                byte[] data = new byte[fileSize];
                new Random().nextBytes(data);
                
                // Write files
                List<Path> filePaths = new ArrayList<>();
                for (int i = 0; i < fileCount; i++) {
                    Path filePath = tempPath.resolve("test_file_" + i + ".dat");
                    Files.write(filePath, data);
                    filePaths.add(filePath);
                }
                
                // Read files back
                long totalBytesRead = 0;
                for (Path filePath : filePaths) {
                    byte[] readData = Files.readAllBytes(filePath);
                    totalBytesRead += readData.length;
                }
                
                // Cleanup
                for (Path filePath : filePaths) {
                    Files.deleteIfExists(filePath);
                }
                Files.deleteIfExists(tempPath);
                
                long end = System.nanoTime();
                times[t] = (end - start) / 1_000_000;
                System.out.printf("Iteration %d: %d ms (bytes read: %d)%n", t + 1, times[t], totalBytesRead);
                
            } catch (IOException e) {
                System.err.println("IO Error: " + e.getMessage());
                times[t] = -1;
            }
        }
        
        return new BenchmarkResult("File Operations", times);
    }
    
    // Test 2: CSV File Processing
    public static BenchmarkResult csvProcessing(int iterations, int recordCount) {
        System.out.println("=== CSV Processing Benchmark ===");
        System.out.println("Record count: " + recordCount);
        
        long[] times = new long[iterations];
        String csvFile = "test_data.csv";
        
        for (int t = 0; t < iterations; t++) {
            long start = System.nanoTime();
            
            try {
                // Generate CSV data
                try (PrintWriter writer = new PrintWriter(new FileWriter(csvFile))) {
                    writer.println("id,name,age,salary,department");
                    Random random = new Random();
                    for (int i = 0; i < recordCount; i++) {
                        writer.printf("%d,Employee_%d,%d,%.2f,Dept_%d%n",
                            i, i, 20 + random.nextInt(40), 30000 + random.nextDouble() * 70000, random.nextInt(10));
                    }
                }
                
                // Read and process CSV
                List<String[]> records = new ArrayList<>();
                try (BufferedReader reader = new BufferedReader(new FileReader(csvFile))) {
                    String line;
                    reader.readLine(); // Skip header
                    while ((line = reader.readLine()) != null) {
                        String[] fields = line.split(",");
                        records.add(fields);
                    }
                }
                
                // Process data
                double totalSalary = 0;
                int totalAge = 0;
                for (String[] record : records) {
                    totalAge += Integer.parseInt(record[2]);
                    totalSalary += Double.parseDouble(record[3]);
                }
                
                // Cleanup
                Files.deleteIfExists(Paths.get(csvFile));
                
                long end = System.nanoTime();
                times[t] = (end - start) / 1_000_000;
                System.out.printf("Iteration %d: %d ms (records: %d, avg age: %.1f, avg salary: %.2f)%n", 
                    t + 1, times[t], records.size(), (double)totalAge / records.size(), totalSalary / records.size());
                
            } catch (IOException e) {
                System.err.println("IO Error: " + e.getMessage());
                times[t] = -1;
            }
        }
        
        return new BenchmarkResult("CSV Processing", times);
    }
    
    // Test 3: Directory Operations
    public static BenchmarkResult directoryOperations(int iterations, int dirCount, int filesPerDir) {
        System.out.println("=== Directory Operations Benchmark ===");
        System.out.println("Directory count: " + dirCount + ", Files per directory: " + filesPerDir);
        
        long[] times = new long[iterations];
        String baseDir = "benchmark_dirs";
        
        for (int t = 0; t < iterations; t++) {
            long start = System.nanoTime();
            
            try {
                Path basePath = Paths.get(baseDir);
                if (!Files.exists(basePath)) {
                    Files.createDirectory(basePath);
                }
                
                List<Path> createdPaths = new ArrayList<>();
                
                // Create directories and files
                for (int d = 0; d < dirCount; d++) {
                    Path dirPath = basePath.resolve("dir_" + d);
                    Files.createDirectory(dirPath);
                    createdPaths.add(dirPath);
                    
                    for (int f = 0; f < filesPerDir; f++) {
                        Path filePath = dirPath.resolve("file_" + f + ".txt");
                        Files.write(filePath, ("Content of file " + f + " in directory " + d).getBytes());
                        createdPaths.add(filePath);
                    }
                }
                
                // List and count files
                int totalFiles = 0;
                try (DirectoryStream<Path> stream = Files.newDirectoryStream(basePath)) {
                    for (Path dir : stream) {
                        if (Files.isDirectory(dir)) {
                            try (DirectoryStream<Path> fileStream = Files.newDirectoryStream(dir)) {
                                for (Path file : fileStream) {
                                    if (Files.isRegularFile(file)) {
                                        totalFiles++;
                                    }
                                }
                            }
                        }
                    }
                }
                
                // Cleanup
                Collections.reverse(createdPaths); // Delete files before directories
                for (Path path : createdPaths) {
                    Files.deleteIfExists(path);
                }
                Files.deleteIfExists(basePath);
                
                long end = System.nanoTime();
                times[t] = (end - start) / 1_000_000;
                System.out.printf("Iteration %d: %d ms (total files: %d)%n", t + 1, times[t], totalFiles);
                
            } catch (IOException e) {
                System.err.println("IO Error: " + e.getMessage());
                times[t] = -1;
            }
        }
        
        return new BenchmarkResult("Directory Operations", times);
    }
    
    // Test 4: Serialization/Deserialization
    public static BenchmarkResult serializationOperations(int iterations, int objectCount) {
        System.out.println("=== Serialization Operations Benchmark ===");
        System.out.println("Object count: " + objectCount);
        
        long[] times = new long[iterations];
        String serFile = "serialized_data.ser";
        
        for (int t = 0; t < iterations; t++) {
            long start = System.nanoTime();
            
            try {
                // Create test objects
                List<TestObject> objects = new ArrayList<>();
                Random random = new Random();
                for (int i = 0; i < objectCount; i++) {
                    objects.add(new TestObject(i, "Object_" + i, random.nextDouble() * 1000));
                }
                
                // Serialize
                try (ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(serFile))) {
                    oos.writeObject(objects);
                }
                
                // Deserialize
                List<TestObject> deserializedObjects;
                try (ObjectInputStream ois = new ObjectInputStream(new FileInputStream(serFile))) {
                    deserializedObjects = (List<TestObject>) ois.readObject();
                }
                
                // Verify
                double totalValue = 0;
                for (TestObject obj : deserializedObjects) {
                    totalValue += obj.value;
                }
                
                // Cleanup
                Files.deleteIfExists(Paths.get(serFile));
                
                long end = System.nanoTime();
                times[t] = (end - start) / 1_000_000;
                System.out.printf("Iteration %d: %d ms (objects: %d, total value: %.2f)%n", 
                    t + 1, times[t], deserializedObjects.size(), totalValue);
                
            } catch (IOException | ClassNotFoundException e) {
                System.err.println("Serialization Error: " + e.getMessage());
                times[t] = -1;
            }
        }
        
        return new BenchmarkResult("Serialization Operations", times);
    }
    
    // Test object for serialization
    static class TestObject implements Serializable {
        private static final long serialVersionUID = 1L;
        public int id;
        public String name;
        public double value;
        
        public TestObject(int id, String name, double value) {
            this.id = id;
            this.name = name;
            this.value = value;
        }
    }
    
    public static void main(String[] args) {
        System.out.println("Java I/O-Bound Benchmark Suite");
        System.out.println("===============================");
        
        List<BenchmarkResult> results = new ArrayList<>();
        
        // Run benchmarks
        results.add(fileOperations(10, 100, 10240)); // 100 files, 10KB each
        System.out.println();
        
        results.add(csvProcessing(10, 50000)); // 50K records
        System.out.println();
        
        results.add(directoryOperations(10, 50, 20)); // 50 dirs, 20 files each
        System.out.println();
        
        results.add(serializationOperations(10, 10000)); // 10K objects
        System.out.println();
        
        // Print summary
        System.out.println("=== SUMMARY ===");
        for (BenchmarkResult result : results) {
            System.out.printf("%-25s: First: %6d ms, Last: %6d ms, Avg: %8.2f ms, Improvement: %.2fx%n",
                result.testName, result.times[0], result.times[result.times.length - 1], 
                result.averageTime, result.improvementRatio);
        }
    }
}
