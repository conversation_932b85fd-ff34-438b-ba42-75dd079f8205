package benchmark;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.*;

public class MemoryUsageBenchmark {
    
    public static class MemorySnapshot {
        public long heapUsed;
        public long heapMax;
        public long nonHeapUsed;
        public long nonHeapMax;
        public long timestamp;
        
        public MemorySnapshot() {
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
            MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();
            
            this.heapUsed = heapUsage.getUsed();
            this.heapMax = heapUsage.getMax();
            this.nonHeapUsed = nonHeapUsage.getUsed();
            this.nonHeapMax = nonHeapUsage.getMax();
            this.timestamp = System.currentTimeMillis();
        }
        
        public long getTotalUsed() {
            return heapUsed + nonHeapUsed;
        }
        
        public double getHeapUsedMB() {
            return heapUsed / (1024.0 * 1024.0);
        }
        
        public double getTotalUsedMB() {
            return getTotalUsed() / (1024.0 * 1024.0);
        }
    }
    
    public static class MemoryBenchmarkResult {
        public String testName;
        public MemorySnapshot baseline;
        public MemorySnapshot peak;
        public MemorySnapshot afterGC;
        public long executionTime;
        public double memoryEfficiency; // MB per second
        
        public MemoryBenchmarkResult(String testName, MemorySnapshot baseline, 
                                   MemorySnapshot peak, MemorySnapshot afterGC, long executionTime) {
            this.testName = testName;
            this.baseline = baseline;
            this.peak = peak;
            this.afterGC = afterGC;
            this.executionTime = executionTime;
            this.memoryEfficiency = (peak.getTotalUsedMB() - baseline.getTotalUsedMB()) / (executionTime / 1000.0);
        }
        
        public double getMemoryOverhead() {
            return peak.getTotalUsedMB() - baseline.getTotalUsedMB();
        }
        
        public double getMemoryRetained() {
            return afterGC.getTotalUsedMB() - baseline.getTotalUsedMB();
        }
    }
    
    // Test 1: Large Object Allocation
    public static MemoryBenchmarkResult largeObjectAllocation() {
        System.out.println("=== Large Object Allocation Memory Test ===");
        
        // Force GC and get baseline
        System.gc();
        try { Thread.sleep(100); } catch (InterruptedException e) {}
        MemorySnapshot baseline = new MemorySnapshot();
        
        long startTime = System.currentTimeMillis();
        MemorySnapshot peak = baseline;
        
        // Allocate large objects
        List<double[]> largeObjects = new ArrayList<>();
        for (int i = 0; i < 1000; i++) {
            double[] largeArray = new double[100000]; // ~800KB each
            Arrays.fill(largeArray, Math.random());
            largeObjects.add(largeArray);
            
            if (i % 100 == 0) {
                MemorySnapshot current = new MemorySnapshot();
                if (current.getTotalUsed() > peak.getTotalUsed()) {
                    peak = current;
                }
                System.out.printf("Allocated %d objects, Memory: %.2f MB%n", 
                    i + 1, current.getTotalUsedMB());
            }
        }
        
        long endTime = System.currentTimeMillis();
        
        // Force GC and measure retained memory
        System.gc();
        try { Thread.sleep(100); } catch (InterruptedException e) {}
        MemorySnapshot afterGC = new MemorySnapshot();
        
        return new MemoryBenchmarkResult("Large Object Allocation", baseline, peak, afterGC, endTime - startTime);
    }
    
    // Test 2: String Operations Memory Usage
    public static MemoryBenchmarkResult stringOperationsMemory() {
        System.out.println("=== String Operations Memory Test ===");
        
        System.gc();
        try { Thread.sleep(100); } catch (InterruptedException e) {}
        MemorySnapshot baseline = new MemorySnapshot();
        
        long startTime = System.currentTimeMillis();
        MemorySnapshot peak = baseline;
        
        // String concatenation and manipulation
        List<String> strings = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        
        for (int i = 0; i < 100000; i++) {
            String str = "String_" + i + "_with_content_" + (i * 13) % 1000;
            strings.add(str);
            sb.append(str).append(" ");
            
            // String operations
            str.toUpperCase();
            str.toLowerCase();
            str.substring(0, Math.min(10, str.length()));
            
            if (i % 10000 == 0) {
                MemorySnapshot current = new MemorySnapshot();
                if (current.getTotalUsed() > peak.getTotalUsed()) {
                    peak = current;
                }
                System.out.printf("Processed %d strings, Memory: %.2f MB%n", 
                    i + 1, current.getTotalUsedMB());
            }
        }
        
        String result = sb.toString();
        long endTime = System.currentTimeMillis();
        
        System.gc();
        try { Thread.sleep(100); } catch (InterruptedException e) {}
        MemorySnapshot afterGC = new MemorySnapshot();
        
        return new MemoryBenchmarkResult("String Operations", baseline, peak, afterGC, endTime - startTime);
    }
    
    // Test 3: Collection Operations Memory Usage
    public static MemoryBenchmarkResult collectionOperationsMemory() {
        System.out.println("=== Collection Operations Memory Test ===");
        
        System.gc();
        try { Thread.sleep(100); } catch (InterruptedException e) {}
        MemorySnapshot baseline = new MemorySnapshot();
        
        long startTime = System.currentTimeMillis();
        MemorySnapshot peak = baseline;
        
        // Multiple collections
        List<Integer> list = new ArrayList<>();
        Set<Integer> set = new HashSet<>();
        Map<Integer, String> map = new HashMap<>();
        
        Random random = new Random(42);
        
        for (int i = 0; i < 500000; i++) {
            int value = random.nextInt(100000);
            list.add(value);
            set.add(value);
            map.put(value, "Value_" + value);
            
            if (i % 50000 == 0) {
                MemorySnapshot current = new MemorySnapshot();
                if (current.getTotalUsed() > peak.getTotalUsed()) {
                    peak = current;
                }
                System.out.printf("Added %d elements, Memory: %.2f MB (List: %d, Set: %d, Map: %d)%n", 
                    i + 1, current.getTotalUsedMB(), list.size(), set.size(), map.size());
            }
        }
        
        // Sort list to trigger additional memory usage
        Collections.sort(list);
        
        long endTime = System.currentTimeMillis();
        
        System.gc();
        try { Thread.sleep(100); } catch (InterruptedException e) {}
        MemorySnapshot afterGC = new MemorySnapshot();
        
        return new MemoryBenchmarkResult("Collection Operations", baseline, peak, afterGC, endTime - startTime);
    }
    
    // Test 4: Recursive Operations Memory Usage
    public static MemoryBenchmarkResult recursiveOperationsMemory() {
        System.out.println("=== Recursive Operations Memory Test ===");
        
        System.gc();
        try { Thread.sleep(100); } catch (InterruptedException e) {}
        MemorySnapshot baseline = new MemorySnapshot();
        
        long startTime = System.currentTimeMillis();
        
        // Deep recursion with object creation
        List<Object> results = new ArrayList<>();
        for (int i = 0; i < 1000; i++) {
            Object result = createNestedObjects(50); // Create nested structure
            results.add(result);
        }
        
        MemorySnapshot peak = new MemorySnapshot();
        long endTime = System.currentTimeMillis();
        
        System.gc();
        try { Thread.sleep(100); } catch (InterruptedException e) {}
        MemorySnapshot afterGC = new MemorySnapshot();
        
        return new MemoryBenchmarkResult("Recursive Operations", baseline, peak, afterGC, endTime - startTime);
    }
    
    private static Object createNestedObjects(int depth) {
        if (depth <= 0) {
            return new double[1000]; // Leaf object
        }
        
        Map<String, Object> nested = new HashMap<>();
        nested.put("data", new double[100]);
        nested.put("left", createNestedObjects(depth - 1));
        nested.put("right", createNestedObjects(depth - 1));
        return nested;
    }
    
    public static void main(String[] args) {
        System.out.println("Java Memory Usage Benchmark Suite");
        System.out.println("==================================");
        System.out.printf("Initial Memory: %.2f MB%n", new MemorySnapshot().getTotalUsedMB());
        System.out.println();
        
        List<MemoryBenchmarkResult> results = new ArrayList<>();
        
        // Run memory benchmarks
        results.add(largeObjectAllocation());
        System.out.println();
        
        results.add(stringOperationsMemory());
        System.out.println();
        
        results.add(collectionOperationsMemory());
        System.out.println();
        
        results.add(recursiveOperationsMemory());
        System.out.println();
        
        // Print memory usage summary
        System.out.println("=== MEMORY USAGE SUMMARY ===");
        System.out.printf("%-25s %12s %12s %12s %12s %12s%n", 
            "Test", "Baseline(MB)", "Peak(MB)", "Overhead(MB)", "Retained(MB)", "Time(ms)");
        System.out.println("-".repeat(90));
        
        for (MemoryBenchmarkResult result : results) {
            System.out.printf("%-25s %12.2f %12.2f %12.2f %12.2f %12d%n",
                result.testName,
                result.baseline.getTotalUsedMB(),
                result.peak.getTotalUsedMB(),
                result.getMemoryOverhead(),
                result.getMemoryRetained(),
                result.executionTime);
        }
        
        System.out.println();
        System.out.println("=== MEMORY EFFICIENCY ANALYSIS ===");
        for (MemoryBenchmarkResult result : results) {
            System.out.printf("%-25s: %.2f MB/sec memory allocation rate%n",
                result.testName, result.memoryEfficiency);
        }
    }
}
