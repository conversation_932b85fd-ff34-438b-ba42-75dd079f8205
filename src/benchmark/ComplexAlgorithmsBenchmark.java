package benchmark;

import java.util.*;

public class ComplexAlgorithmsBenchmark {
    
    public static class BenchmarkResult {
        public String testName;
        public long[] times;
        public double averageTime;
        public double improvementRatio;
        
        public BenchmarkResult(String testName, long[] times) {
            this.testName = testName;
            this.times = times;
            this.averageTime = calculateAverage(times);
            this.improvementRatio = times.length > 1 ? (double)times[0] / times[times.length - 1] : 1.0;
        }
        
        private double calculateAverage(long[] times) {
            long sum = 0;
            for (long time : times) sum += time;
            return (double)sum / times.length;
        }
    }
    
    // Test 1: QuickSort Algorithm
    public static BenchmarkResult quickSortBenchmark(int iterations, int arraySize) {
        System.out.println("=== QuickSort Benchmark ===");
        System.out.println("Array size: " + arraySize);
        
        long[] times = new long[iterations];
        Random random = new Random();
        
        for (int t = 0; t < iterations; t++) {
            // Generate random array
            int[] array = new int[arraySize];
            for (int i = 0; i < arraySize; i++) {
                array[i] = random.nextInt(arraySize);
            }
            
            long start = System.nanoTime();
            quickSort(array, 0, array.length - 1);
            long end = System.nanoTime();
            
            times[t] = (end - start) / 1_000_000;
            System.out.printf("Iteration %d: %d ms (first: %d, last: %d)%n", 
                t + 1, times[t], array[0], array[array.length - 1]);
        }
        
        return new BenchmarkResult("QuickSort", times);
    }
    
    private static void quickSort(int[] arr, int low, int high) {
        if (low < high) {
            int pi = partition(arr, low, high);
            quickSort(arr, low, pi - 1);
            quickSort(arr, pi + 1, high);
        }
    }
    
    private static int partition(int[] arr, int low, int high) {
        int pivot = arr[high];
        int i = (low - 1);
        
        for (int j = low; j < high; j++) {
            if (arr[j] <= pivot) {
                i++;
                int temp = arr[i];
                arr[i] = arr[j];
                arr[j] = temp;
            }
        }
        
        int temp = arr[i + 1];
        arr[i + 1] = arr[high];
        arr[high] = temp;
        
        return i + 1;
    }
    
    // Test 2: Binary Search Tree Operations
    public static BenchmarkResult binarySearchTreeBenchmark(int iterations, int nodeCount) {
        System.out.println("=== Binary Search Tree Benchmark ===");
        System.out.println("Node count: " + nodeCount);
        
        long[] times = new long[iterations];
        Random random = new Random();
        
        for (int t = 0; t < iterations; t++) {
            long start = System.nanoTime();
            
            BinarySearchTree bst = new BinarySearchTree();
            
            // Insert nodes
            for (int i = 0; i < nodeCount; i++) {
                bst.insert(random.nextInt(nodeCount * 2));
            }
            
            // Search operations
            int foundCount = 0;
            for (int i = 0; i < nodeCount / 10; i++) {
                if (bst.search(random.nextInt(nodeCount * 2))) {
                    foundCount++;
                }
            }
            
            // Tree traversal
            List<Integer> inorderResult = bst.inorderTraversal();
            
            long end = System.nanoTime();
            times[t] = (end - start) / 1_000_000;
            System.out.printf("Iteration %d: %d ms (found: %d, traversal size: %d)%n", 
                t + 1, times[t], foundCount, inorderResult.size());
        }
        
        return new BenchmarkResult("Binary Search Tree", times);
    }
    
    // Test 3: Graph Algorithms (Dijkstra's shortest path)
    public static BenchmarkResult dijkstraAlgorithm(int iterations, int nodeCount) {
        System.out.println("=== Dijkstra's Algorithm Benchmark ===");
        System.out.println("Node count: " + nodeCount);
        
        long[] times = new long[iterations];
        Random random = new Random();
        
        for (int t = 0; t < iterations; t++) {
            long start = System.nanoTime();
            
            // Create graph
            Graph graph = new Graph(nodeCount);
            
            // Add random edges
            int edgeCount = nodeCount * 3; // Dense graph
            for (int i = 0; i < edgeCount; i++) {
                int from = random.nextInt(nodeCount);
                int to = random.nextInt(nodeCount);
                int weight = random.nextInt(100) + 1;
                graph.addEdge(from, to, weight);
            }
            
            // Run Dijkstra from source 0
            int[] distances = graph.dijkstra(0);
            
            // Count reachable nodes
            int reachableCount = 0;
            for (int dist : distances) {
                if (dist != Integer.MAX_VALUE) {
                    reachableCount++;
                }
            }
            
            long end = System.nanoTime();
            times[t] = (end - start) / 1_000_000;
            System.out.printf("Iteration %d: %d ms (reachable nodes: %d)%n", 
                t + 1, times[t], reachableCount);
        }
        
        return new BenchmarkResult("Dijkstra Algorithm", times);
    }
    
    // Test 4: Dynamic Programming (Longest Common Subsequence)
    public static BenchmarkResult longestCommonSubsequence(int iterations, int stringLength) {
        System.out.println("=== Longest Common Subsequence Benchmark ===");
        System.out.println("String length: " + stringLength);
        
        long[] times = new long[iterations];
        Random random = new Random();
        
        for (int t = 0; t < iterations; t++) {
            // Generate random strings
            StringBuilder sb1 = new StringBuilder();
            StringBuilder sb2 = new StringBuilder();
            
            for (int i = 0; i < stringLength; i++) {
                sb1.append((char)('A' + random.nextInt(4))); // A, B, C, D
                sb2.append((char)('A' + random.nextInt(4)));
            }
            
            String str1 = sb1.toString();
            String str2 = sb2.toString();
            
            long start = System.nanoTime();
            int lcsLength = lcs(str1, str2);
            long end = System.nanoTime();
            
            times[t] = (end - start) / 1_000_000;
            System.out.printf("Iteration %d: %d ms (LCS length: %d)%n", 
                t + 1, times[t], lcsLength);
        }
        
        return new BenchmarkResult("Longest Common Subsequence", times);
    }
    
    private static int lcs(String str1, String str2) {
        int m = str1.length();
        int n = str2.length();
        int[][] dp = new int[m + 1][n + 1];
        
        for (int i = 1; i <= m; i++) {
            for (int j = 1; j <= n; j++) {
                if (str1.charAt(i - 1) == str2.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1] + 1;
                } else {
                    dp[i][j] = Math.max(dp[i - 1][j], dp[i][j - 1]);
                }
            }
        }
        
        return dp[m][n];
    }
    
    // Test 5: Backtracking (N-Queens problem)
    public static BenchmarkResult nQueensProblem(int iterations, int boardSize) {
        System.out.println("=== N-Queens Problem Benchmark ===");
        System.out.println("Board size: " + boardSize);
        
        long[] times = new long[iterations];
        
        for (int t = 0; t < iterations; t++) {
            long start = System.nanoTime();
            
            NQueens nQueens = new NQueens(boardSize);
            int solutionCount = nQueens.solveNQueens();
            
            long end = System.nanoTime();
            times[t] = (end - start) / 1_000_000;
            System.out.printf("Iteration %d: %d ms (solutions found: %d)%n", 
                t + 1, times[t], solutionCount);
        }
        
        return new BenchmarkResult("N-Queens Problem", times);
    }
    
    // Supporting classes
    static class BinarySearchTree {
        class Node {
            int data;
            Node left, right;
            
            Node(int data) {
                this.data = data;
            }
        }
        
        private Node root;
        
        public void insert(int data) {
            root = insertRec(root, data);
        }
        
        private Node insertRec(Node root, int data) {
            if (root == null) {
                root = new Node(data);
                return root;
            }
            
            if (data < root.data) {
                root.left = insertRec(root.left, data);
            } else if (data > root.data) {
                root.right = insertRec(root.right, data);
            }
            
            return root;
        }
        
        public boolean search(int data) {
            return searchRec(root, data);
        }
        
        private boolean searchRec(Node root, int data) {
            if (root == null) return false;
            if (root.data == data) return true;
            
            return data < root.data ? searchRec(root.left, data) : searchRec(root.right, data);
        }
        
        public List<Integer> inorderTraversal() {
            List<Integer> result = new ArrayList<>();
            inorderRec(root, result);
            return result;
        }
        
        private void inorderRec(Node root, List<Integer> result) {
            if (root != null) {
                inorderRec(root.left, result);
                result.add(root.data);
                inorderRec(root.right, result);
            }
        }
    }
    
    static class Graph {
        private int vertices;
        private List<List<Edge>> adjacencyList;
        
        class Edge {
            int destination;
            int weight;
            
            Edge(int destination, int weight) {
                this.destination = destination;
                this.weight = weight;
            }
        }
        
        public Graph(int vertices) {
            this.vertices = vertices;
            adjacencyList = new ArrayList<>();
            for (int i = 0; i < vertices; i++) {
                adjacencyList.add(new ArrayList<>());
            }
        }
        
        public void addEdge(int source, int destination, int weight) {
            adjacencyList.get(source).add(new Edge(destination, weight));
        }
        
        public int[] dijkstra(int source) {
            int[] distances = new int[vertices];
            Arrays.fill(distances, Integer.MAX_VALUE);
            distances[source] = 0;
            
            PriorityQueue<Integer> pq = new PriorityQueue<>((a, b) -> distances[a] - distances[b]);
            pq.offer(source);
            
            while (!pq.isEmpty()) {
                int u = pq.poll();
                
                for (Edge edge : adjacencyList.get(u)) {
                    int v = edge.destination;
                    int weight = edge.weight;
                    
                    if (distances[u] != Integer.MAX_VALUE && distances[u] + weight < distances[v]) {
                        distances[v] = distances[u] + weight;
                        pq.offer(v);
                    }
                }
            }
            
            return distances;
        }
    }
    
    static class NQueens {
        private int n;
        private int solutionCount;
        
        public NQueens(int n) {
            this.n = n;
            this.solutionCount = 0;
        }
        
        public int solveNQueens() {
            solutionCount = 0;
            int[] board = new int[n];
            solve(board, 0);
            return solutionCount;
        }
        
        private void solve(int[] board, int row) {
            if (row == n) {
                solutionCount++;
                return;
            }
            
            for (int col = 0; col < n; col++) {
                if (isSafe(board, row, col)) {
                    board[row] = col;
                    solve(board, row + 1);
                }
            }
        }
        
        private boolean isSafe(int[] board, int row, int col) {
            for (int i = 0; i < row; i++) {
                if (board[i] == col || Math.abs(board[i] - col) == Math.abs(i - row)) {
                    return false;
                }
            }
            return true;
        }
    }
    
    public static void main(String[] args) {
        System.out.println("Java Complex Algorithms Benchmark Suite");
        System.out.println("========================================");

        List<BenchmarkResult> results = new ArrayList<>();

        // Run benchmarks
        results.add(quickSortBenchmark(10, 100000));
        System.out.println();

        results.add(binarySearchTreeBenchmark(10, 50000));
        System.out.println();

        results.add(dijkstraAlgorithm(10, 1000));
        System.out.println();

        results.add(longestCommonSubsequence(10, 500));
        System.out.println();

        results.add(nQueensProblem(10, 10));
        System.out.println();

        // Print summary
        System.out.println("=== SUMMARY ===");
        for (BenchmarkResult result : results) {
            System.out.printf("%-30s: First: %6d ms, Last: %6d ms, Avg: %8.2f ms, Improvement: %.2fx%n",
                result.testName, result.times[0], result.times[result.times.length - 1],
                result.averageTime, result.improvementRatio);
        }
    }
}
