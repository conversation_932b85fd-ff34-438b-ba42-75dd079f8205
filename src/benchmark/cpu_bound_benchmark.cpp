#include <iostream>
#include <vector>
#include <chrono>
#include <cmath>
#include <random>
#include <iomanip>

class BenchmarkResult {
public:
    std::string testName;
    std::vector<long long> times;
    double averageTime;
    double improvementRatio;
    
    BenchmarkResult(const std::string& name, const std::vector<long long>& t) 
        : testName(name), times(t) {
        calculateStats();
    }
    
private:
    void calculateStats() {
        long long sum = 0;
        for (long long time : times) sum += time;
        averageTime = (double)sum / times.size();
        improvementRatio = times.size() > 1 ? (double)times[0] / times[times.size() - 1] : 1.0;
    }
};

// Test 1: Matrix Multiplication
BenchmarkResult matrixMultiplication(int iterations, int matrixSize, int innerLoops) {
    std::cout << "=== Matrix Multiplication Benchmark ===" << std::endl;
    std::cout << "Matrix size: " << matrixSize << "x" << matrixSize << ", Inner loops: " << innerLoops << std::endl;
    
    std::vector<std::vector<double>> A(matrixSize, std::vector<double>(matrixSize));
    std::vector<std::vector<double>> B(matrixSize, std::vector<double>(matrixSize));
    std::vector<std::vector<double>> C(matrixSize, std::vector<double>(matrixSize));
    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<> dis(0.0, 1.0);
    
    // Initialize matrices
    for (int i = 0; i < matrixSize; i++) {
        for (int j = 0; j < matrixSize; j++) {
            A[i][j] = dis(gen);
            B[i][j] = dis(gen);
        }
    }
    
    std::vector<long long> times(iterations);
    
    for (int t = 0; t < iterations; t++) {
        // Reset result matrix
        for (int i = 0; i < matrixSize; i++) {
            for (int j = 0; j < matrixSize; j++) {
                C[i][j] = 0.0;
            }
        }
        
        auto start = std::chrono::high_resolution_clock::now();
        
        for (int loop = 0; loop < innerLoops; loop++) {
            for (int i = 0; i < matrixSize; i++) {
                for (int k = 0; k < matrixSize; k++) {
                    double aik = A[i][k];
                    for (int j = 0; j < matrixSize; j++) {
                        C[i][j] += aik * B[k][j];
                    }
                }
            }
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        times[t] = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
        std::cout << "Iteration " << (t + 1) << ": " << times[t] << " ms" << std::endl;
    }
    
    return BenchmarkResult("Matrix Multiplication", times);
}

// Test 2: Prime Number Calculation
BenchmarkResult primeCalculation(int iterations, int maxNumber) {
    std::cout << "=== Prime Number Calculation Benchmark ===" << std::endl;
    std::cout << "Finding primes up to: " << maxNumber << std::endl;
    
    std::vector<long long> times(iterations);
    
    auto isPrime = [](int n) -> bool {
        if (n < 2) return false;
        if (n == 2) return true;
        if (n % 2 == 0) return false;
        
        for (int i = 3; i * i <= n; i += 2) {
            if (n % i == 0) return false;
        }
        return true;
    };
    
    for (int t = 0; t < iterations; t++) {
        auto start = std::chrono::high_resolution_clock::now();
        
        std::vector<int> primes;
        for (int num = 2; num <= maxNumber; num++) {
            if (isPrime(num)) {
                primes.push_back(num);
            }
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        times[t] = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
        std::cout << "Iteration " << (t + 1) << ": " << times[t] << " ms (found " << primes.size() << " primes)" << std::endl;
    }
    
    return BenchmarkResult("Prime Calculation", times);
}

// Test 3: Mathematical Operations
BenchmarkResult mathematicalOperations(int iterations, int operationCount) {
    std::cout << "=== Mathematical Operations Benchmark ===" << std::endl;
    std::cout << "Operations count: " << operationCount << std::endl;
    
    std::vector<long long> times(iterations);
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<> dis(0.0, 100.0);
    
    for (int t = 0; t < iterations; t++) {
        auto start = std::chrono::high_resolution_clock::now();
        
        double result = 0.0;
        for (int i = 0; i < operationCount; i++) {
            double x = dis(gen);
            result += sin(x) * cos(x) + sqrt(x) + log(x + 1) + pow(x, 0.5);
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        times[t] = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
        std::cout << "Iteration " << (t + 1) << ": " << times[t] << " ms (result: " << std::fixed << std::setprecision(6) << result << ")" << std::endl;
    }
    
    return BenchmarkResult("Mathematical Operations", times);
}

// Test 4: Fibonacci Calculation
BenchmarkResult fibonacciCalculation(int iterations, int fibNumber) {
    std::cout << "=== Fibonacci Calculation Benchmark ===" << std::endl;
    std::cout << "Calculating fibonacci(" << fibNumber << ")" << std::endl;
    
    std::vector<long long> times(iterations);
    
    auto fibonacciIterative = [](int n) -> long long {
        if (n <= 1) return n;
        long long a = 0, b = 1;
        for (int i = 2; i <= n; i++) {
            long long temp = a + b;
            a = b;
            b = temp;
        }
        return b;
    };
    
    for (int t = 0; t < iterations; t++) {
        auto start = std::chrono::high_resolution_clock::now();
        
        long long result = fibonacciIterative(fibNumber);
        
        auto end = std::chrono::high_resolution_clock::now();
        times[t] = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
        std::cout << "Iteration " << (t + 1) << ": " << times[t] << " ms (fib(" << fibNumber << ") = " << result << ")" << std::endl;
    }
    
    return BenchmarkResult("Fibonacci Calculation", times);
}

int main() {
    std::cout << "C++ CPU-Bound Benchmark Suite" << std::endl;
    std::cout << "==============================" << std::endl;
    
    std::vector<BenchmarkResult> results;
    
    // Run benchmarks
    results.push_back(matrixMultiplication(10, 100, 1000));
    std::cout << std::endl;
    
    results.push_back(primeCalculation(10, 50000));
    std::cout << std::endl;
    
    results.push_back(mathematicalOperations(10, 1000000));
    std::cout << std::endl;
    
    results.push_back(fibonacciCalculation(10, 1000000));
    std::cout << std::endl;
    
    // Print summary
    std::cout << "=== SUMMARY ===" << std::endl;
    for (const auto& result : results) {
        std::cout << std::left << std::setw(25) << result.testName << ": "
                  << "First: " << std::setw(6) << result.times[0] << " ms, "
                  << "Last: " << std::setw(6) << result.times[result.times.size() - 1] << " ms, "
                  << "Avg: " << std::setw(8) << std::fixed << std::setprecision(2) << result.averageTime << " ms, "
                  << "Improvement: " << std::setprecision(2) << result.improvementRatio << "x" << std::endl;
    }
    
    return 0;
}
