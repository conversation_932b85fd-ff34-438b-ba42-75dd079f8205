#include <iostream>
#include <vector>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <algorithm>
#include <chrono>
#include <random>
#include <memory>
#include <fstream>

#ifdef __linux__
#include <sys/resource.h>
#include <unistd.h>
#endif

class MemoryTracker {
private:
    size_t peak_memory_kb = 0;
    
public:
    size_t getCurrentMemoryKB() {
#ifdef __linux__
        std::ifstream status_file("/proc/self/status");
        std::string line;
        while (std::getline(status_file, line)) {
            if (line.substr(0, 6) == "VmRSS:") {
                size_t memory_kb = std::stoul(line.substr(7));
                peak_memory_kb = std::max(peak_memory_kb, memory_kb);
                return memory_kb;
            }
        }
#endif
        return 0;
    }
    
    double getCurrentMemoryMB() {
        return getCurrentMemoryKB() / 1024.0;
    }
    
    size_t getPeakMemoryKB() {
        getCurrentMemoryKB(); // Update peak
        return peak_memory_kb;
    }
    
    double getPeakMemoryMB() {
        return getPeakMemoryKB() / 1024.0;
    }
};

struct MemoryBenchmarkResult {
    std::string testName;
    double baselineMemoryMB;
    double peakMemoryMB;
    double finalMemoryMB;
    long executionTimeMs;
    
    MemoryBenchmarkResult(const std::string& name, double baseline, double peak, double final, long time)
        : testName(name), baselineMemoryMB(baseline), peakMemoryMB(peak), finalMemoryMB(final), executionTimeMs(time) {}
    
    double getMemoryOverhead() const {
        return peakMemoryMB - baselineMemoryMB;
    }
    
    double getMemoryRetained() const {
        return finalMemoryMB - baselineMemoryMB;
    }
};

// Test 1: Large Object Allocation
MemoryBenchmarkResult largeObjectAllocation() {
    std::cout << "=== Large Object Allocation Memory Test ===" << std::endl;
    
    MemoryTracker tracker;
    double baseline = tracker.getCurrentMemoryMB();
    
    auto start = std::chrono::high_resolution_clock::now();
    
    // Allocate large objects
    std::vector<std::unique_ptr<std::vector<double>>> largeObjects;
    largeObjects.reserve(1000);
    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<> dis(0.0, 1.0);
    
    for (int i = 0; i < 1000; i++) {
        auto largeArray = std::make_unique<std::vector<double>>(100000); // ~800KB each
        for (auto& val : *largeArray) {
            val = dis(gen);
        }
        largeObjects.push_back(std::move(largeArray));
        
        if (i % 100 == 0) {
            double currentMemory = tracker.getCurrentMemoryMB();
            std::cout << "Allocated " << (i + 1) << " objects, Memory: " << currentMemory << " MB" << std::endl;
        }
    }
    
    double peak = tracker.getPeakMemoryMB();
    auto end = std::chrono::high_resolution_clock::now();
    
    // Clear objects
    largeObjects.clear();
    double final = tracker.getCurrentMemoryMB();
    
    long executionTime = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
    
    return MemoryBenchmarkResult("Large Object Allocation", baseline, peak, final, executionTime);
}

// Test 2: String Operations Memory Usage
MemoryBenchmarkResult stringOperationsMemory() {
    std::cout << "=== String Operations Memory Test ===" << std::endl;
    
    MemoryTracker tracker;
    double baseline = tracker.getCurrentMemoryMB();
    
    auto start = std::chrono::high_resolution_clock::now();
    
    // String operations
    std::vector<std::string> strings;
    strings.reserve(100000);
    std::string result;
    
    for (int i = 0; i < 100000; i++) {
        std::string str = "String_" + std::to_string(i) + "_with_content_" + std::to_string((i * 13) % 1000);
        strings.push_back(str);
        result += str + " ";
        
        // String operations
        std::string upper = str;
        std::transform(upper.begin(), upper.end(), upper.begin(), ::toupper);
        std::string lower = str;
        std::transform(lower.begin(), lower.end(), lower.begin(), ::tolower);
        std::string sub = str.substr(0, std::min(10, (int)str.length()));
        
        if (i % 10000 == 0) {
            double currentMemory = tracker.getCurrentMemoryMB();
            std::cout << "Processed " << (i + 1) << " strings, Memory: " << currentMemory << " MB" << std::endl;
        }
    }
    
    double peak = tracker.getPeakMemoryMB();
    auto end = std::chrono::high_resolution_clock::now();
    
    // Clear strings
    strings.clear();
    result.clear();
    double final = tracker.getCurrentMemoryMB();
    
    long executionTime = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
    
    return MemoryBenchmarkResult("String Operations", baseline, peak, final, executionTime);
}

// Test 3: Collection Operations Memory Usage
MemoryBenchmarkResult collectionOperationsMemory() {
    std::cout << "=== Collection Operations Memory Test ===" << std::endl;
    
    MemoryTracker tracker;
    double baseline = tracker.getCurrentMemoryMB();
    
    auto start = std::chrono::high_resolution_clock::now();
    
    // Multiple collections
    std::vector<int> vec;
    std::unordered_set<int> set;
    std::unordered_map<int, std::string> map;
    
    vec.reserve(500000);
    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 99999);
    
    for (int i = 0; i < 500000; i++) {
        int value = dis(gen);
        vec.push_back(value);
        set.insert(value);
        map[value] = "Value_" + std::to_string(value);
        
        if (i % 50000 == 0) {
            double currentMemory = tracker.getCurrentMemoryMB();
            std::cout << "Added " << (i + 1) << " elements, Memory: " << currentMemory 
                     << " MB (Vec: " << vec.size() << ", Set: " << set.size() 
                     << ", Map: " << map.size() << ")" << std::endl;
        }
    }
    
    // Sort vector to trigger additional memory usage
    std::sort(vec.begin(), vec.end());
    
    double peak = tracker.getPeakMemoryMB();
    auto end = std::chrono::high_resolution_clock::now();
    
    // Clear collections
    vec.clear();
    set.clear();
    map.clear();
    double final = tracker.getCurrentMemoryMB();
    
    long executionTime = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
    
    return MemoryBenchmarkResult("Collection Operations", baseline, peak, final, executionTime);
}

// Test 4: Recursive Operations Memory Usage
struct NestedObject {
    std::vector<double> data;
    std::unique_ptr<NestedObject> left;
    std::unique_ptr<NestedObject> right;
    
    NestedObject() : data(100) {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_real_distribution<> dis(0.0, 1.0);
        for (auto& val : data) {
            val = dis(gen);
        }
    }
};

std::unique_ptr<NestedObject> createNestedObjects(int depth) {
    auto obj = std::make_unique<NestedObject>();
    if (depth > 0) {
        obj->left = createNestedObjects(depth - 1);
        obj->right = createNestedObjects(depth - 1);
    }
    return obj;
}

MemoryBenchmarkResult recursiveOperationsMemory() {
    std::cout << "=== Recursive Operations Memory Test ===" << std::endl;
    
    MemoryTracker tracker;
    double baseline = tracker.getCurrentMemoryMB();
    
    auto start = std::chrono::high_resolution_clock::now();
    
    // Create nested objects
    std::vector<std::unique_ptr<NestedObject>> results;
    for (int i = 0; i < 100; i++) { // Reduced from 1000 to avoid stack overflow
        auto result = createNestedObjects(10); // Reduced depth
        results.push_back(std::move(result));
    }
    
    double peak = tracker.getPeakMemoryMB();
    auto end = std::chrono::high_resolution_clock::now();
    
    // Clear objects
    results.clear();
    double final = tracker.getCurrentMemoryMB();
    
    long executionTime = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
    
    return MemoryBenchmarkResult("Recursive Operations", baseline, peak, final, executionTime);
}

int main() {
    std::cout << "C++ Memory Usage Benchmark Suite" << std::endl;
    std::cout << "=================================" << std::endl;
    
    MemoryTracker tracker;
    std::cout << "Initial Memory: " << tracker.getCurrentMemoryMB() << " MB" << std::endl;
    std::cout << std::endl;
    
    std::vector<MemoryBenchmarkResult> results;
    
    // Run memory benchmarks
    results.push_back(largeObjectAllocation());
    std::cout << std::endl;
    
    results.push_back(stringOperationsMemory());
    std::cout << std::endl;
    
    results.push_back(collectionOperationsMemory());
    std::cout << std::endl;
    
    results.push_back(recursiveOperationsMemory());
    std::cout << std::endl;
    
    // Print memory usage summary
    std::cout << "=== MEMORY USAGE SUMMARY ===" << std::endl;
    std::cout << std::left << std::setw(25) << "Test" 
              << std::setw(12) << "Baseline(MB)" 
              << std::setw(12) << "Peak(MB)" 
              << std::setw(12) << "Overhead(MB)" 
              << std::setw(12) << "Retained(MB)" 
              << std::setw(12) << "Time(ms)" << std::endl;
    std::cout << std::string(85, '-') << std::endl;
    
    for (const auto& result : results) {
        std::cout << std::left << std::setw(25) << result.testName
                  << std::setw(12) << std::fixed << std::setprecision(2) << result.baselineMemoryMB
                  << std::setw(12) << result.peakMemoryMB
                  << std::setw(12) << result.getMemoryOverhead()
                  << std::setw(12) << result.getMemoryRetained()
                  << std::setw(12) << result.executionTimeMs << std::endl;
    }
    
    return 0;
}
