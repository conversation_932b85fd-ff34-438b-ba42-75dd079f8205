package benchmark;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.IntStream;

public class MultithreadingBenchmark {
    
    public static class ThreadingResult {
        public String testName;
        public int threadCount;
        public long singleThreadTime;
        public long multiThreadTime;
        public double speedup;
        public double efficiency;
        
        public ThreadingResult(String testName, int threadCount, long singleThreadTime, long multiThreadTime) {
            this.testName = testName;
            this.threadCount = threadCount;
            this.singleThreadTime = singleThreadTime;
            this.multiThreadTime = multiThreadTime;
            this.speedup = (double) singleThreadTime / multiThreadTime;
            this.efficiency = speedup / threadCount;
        }
    }
    
    // Test 1: Parallel Matrix Multiplication
    public static ThreadingResult parallelMatrixMultiplication(int threadCount) {
        System.out.println("=== Parallel Matrix Multiplication Test ===");
        System.out.println("Thread count: " + threadCount);
        
        int matrixSize = 500;
        double[][] A = new double[matrixSize][matrixSize];
        double[][] B = new double[matrixSize][matrixSize];
        double[][] C1 = new double[matrixSize][matrixSize]; // Single thread result
        double[][] C2 = new double[matrixSize][matrixSize]; // Multi thread result
        
        // Initialize matrices
        Random random = new Random(42);
        for (int i = 0; i < matrixSize; i++) {
            for (int j = 0; j < matrixSize; j++) {
                A[i][j] = random.nextDouble();
                B[i][j] = random.nextDouble();
            }
        }
        
        // Single-threaded version
        long startSingle = System.nanoTime();
        for (int i = 0; i < matrixSize; i++) {
            for (int k = 0; k < matrixSize; k++) {
                double aik = A[i][k];
                for (int j = 0; j < matrixSize; j++) {
                    C1[i][j] += aik * B[k][j];
                }
            }
        }
        long singleThreadTime = (System.nanoTime() - startSingle) / 1_000_000;
        
        // Multi-threaded version using parallel streams
        long startMulti = System.nanoTime();
        IntStream.range(0, matrixSize).parallel().forEach(i -> {
            for (int k = 0; k < matrixSize; k++) {
                double aik = A[i][k];
                for (int j = 0; j < matrixSize; j++) {
                    C2[i][j] += aik * B[k][j];
                }
            }
        });
        long multiThreadTime = (System.nanoTime() - startMulti) / 1_000_000;
        
        System.out.printf("Single-thread: %d ms, Multi-thread: %d ms, Speedup: %.2fx%n", 
            singleThreadTime, multiThreadTime, (double)singleThreadTime / multiThreadTime);
        
        return new ThreadingResult("Parallel Matrix Multiplication", threadCount, singleThreadTime, multiThreadTime);
    }
    
    // Test 2: Parallel Prime Calculation
    public static ThreadingResult parallelPrimeCalculation(int threadCount) {
        System.out.println("=== Parallel Prime Calculation Test ===");
        System.out.println("Thread count: " + threadCount);
        
        int maxNumber = 100000;
        
        // Single-threaded version
        long startSingle = System.nanoTime();
        List<Integer> primesSingle = new ArrayList<>();
        for (int num = 2; num <= maxNumber; num++) {
            if (isPrime(num)) {
                primesSingle.add(num);
            }
        }
        long singleThreadTime = (System.nanoTime() - startSingle) / 1_000_000;
        
        // Multi-threaded version
        long startMulti = System.nanoTime();
        List<Integer> primesMulti = IntStream.rangeClosed(2, maxNumber)
            .parallel()
            .filter(MultithreadingBenchmark::isPrime)
            .boxed()
            .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
        long multiThreadTime = (System.nanoTime() - startMulti) / 1_000_000;
        
        System.out.printf("Single-thread: %d ms (%d primes), Multi-thread: %d ms (%d primes), Speedup: %.2fx%n", 
            singleThreadTime, primesSingle.size(), multiThreadTime, primesMulti.size(), 
            (double)singleThreadTime / multiThreadTime);
        
        return new ThreadingResult("Parallel Prime Calculation", threadCount, singleThreadTime, multiThreadTime);
    }
    
    // Test 3: Producer-Consumer Pattern
    public static ThreadingResult producerConsumerTest(int threadCount) {
        System.out.println("=== Producer-Consumer Test ===");
        System.out.println("Thread count: " + threadCount);
        
        int itemCount = 1000000;
        
        // Single-threaded version
        long startSingle = System.nanoTime();
        List<Integer> items = new ArrayList<>();
        AtomicLong sum = new AtomicLong(0);
        
        for (int i = 0; i < itemCount; i++) {
            items.add(i);
            sum.addAndGet(i * i); // Simulate processing
        }
        long singleThreadTime = (System.nanoTime() - startSingle) / 1_000_000;
        
        // Multi-threaded version
        long startMulti = System.nanoTime();
        BlockingQueue<Integer> queue = new ArrayBlockingQueue<>(10000);
        AtomicLong multiSum = new AtomicLong(0);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        // Producer
        Future<?> producer = executor.submit(() -> {
            try {
                for (int i = 0; i < itemCount; i++) {
                    queue.put(i);
                }
                // Add sentinel values to signal end
                for (int i = 0; i < threadCount / 2; i++) {
                    queue.put(-1);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        // Consumers
        List<Future<?>> consumers = new ArrayList<>();
        for (int i = 0; i < threadCount / 2; i++) {
            consumers.add(executor.submit(() -> {
                try {
                    while (true) {
                        Integer item = queue.take();
                        if (item == -1) break;
                        multiSum.addAndGet(item * item);
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }));
        }
        
        try {
            producer.get();
            for (Future<?> consumer : consumers) {
                consumer.get();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        executor.shutdown();
        long multiThreadTime = (System.nanoTime() - startMulti) / 1_000_000;
        
        System.out.printf("Single-thread: %d ms (sum: %d), Multi-thread: %d ms (sum: %d), Speedup: %.2fx%n", 
            singleThreadTime, sum.get(), multiThreadTime, multiSum.get(), 
            (double)singleThreadTime / multiThreadTime);
        
        return new ThreadingResult("Producer-Consumer", threadCount, singleThreadTime, multiThreadTime);
    }
    
    // Test 4: Parallel Sorting
    public static ThreadingResult parallelSorting(int threadCount) {
        System.out.println("=== Parallel Sorting Test ===");
        System.out.println("Thread count: " + threadCount);
        
        int arraySize = 10000000;
        
        // Generate random array
        Random random = new Random(42);
        int[] array1 = new int[arraySize];
        int[] array2 = new int[arraySize];
        
        for (int i = 0; i < arraySize; i++) {
            int value = random.nextInt(arraySize);
            array1[i] = value;
            array2[i] = value;
        }
        
        // Single-threaded sorting
        long startSingle = System.nanoTime();
        Arrays.sort(array1);
        long singleThreadTime = (System.nanoTime() - startSingle) / 1_000_000;
        
        // Multi-threaded sorting using parallel sort
        long startMulti = System.nanoTime();
        Arrays.parallelSort(array2);
        long multiThreadTime = (System.nanoTime() - startMulti) / 1_000_000;
        
        System.out.printf("Single-thread: %d ms, Multi-thread: %d ms, Speedup: %.2fx%n", 
            singleThreadTime, multiThreadTime, (double)singleThreadTime / multiThreadTime);
        
        return new ThreadingResult("Parallel Sorting", threadCount, singleThreadTime, multiThreadTime);
    }
    
    // Test 5: Concurrent Map Operations
    public static ThreadingResult concurrentMapOperations(int threadCount) {
        System.out.println("=== Concurrent Map Operations Test ===");
        System.out.println("Thread count: " + threadCount);
        
        int operationCount = 1000000;
        
        // Single-threaded version
        long startSingle = System.nanoTime();
        Map<Integer, String> singleMap = new HashMap<>();
        Random random = new Random(42);
        
        for (int i = 0; i < operationCount; i++) {
            int key = random.nextInt(operationCount / 10);
            singleMap.put(key, "Value_" + key);
            singleMap.get(key);
        }
        long singleThreadTime = (System.nanoTime() - startSingle) / 1_000_000;
        
        // Multi-threaded version
        long startMulti = System.nanoTime();
        ConcurrentHashMap<Integer, String> concurrentMap = new ConcurrentHashMap<>();
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        List<Future<?>> futures = new ArrayList<>();
        for (int t = 0; t < threadCount; t++) {
            futures.add(executor.submit(() -> {
                Random threadRandom = new Random(42);
                for (int i = 0; i < operationCount / threadCount; i++) {
                    int key = threadRandom.nextInt(operationCount / 10);
                    concurrentMap.put(key, "Value_" + key);
                    concurrentMap.get(key);
                }
            }));
        }
        
        try {
            for (Future<?> future : futures) {
                future.get();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        executor.shutdown();
        long multiThreadTime = (System.nanoTime() - startMulti) / 1_000_000;
        
        System.out.printf("Single-thread: %d ms (map size: %d), Multi-thread: %d ms (map size: %d), Speedup: %.2fx%n", 
            singleThreadTime, singleMap.size(), multiThreadTime, concurrentMap.size(), 
            (double)singleThreadTime / multiThreadTime);
        
        return new ThreadingResult("Concurrent Map Operations", threadCount, singleThreadTime, multiThreadTime);
    }
    
    private static boolean isPrime(int n) {
        if (n < 2) return false;
        if (n == 2) return true;
        if (n % 2 == 0) return false;
        
        for (int i = 3; i * i <= n; i += 2) {
            if (n % i == 0) return false;
        }
        return true;
    }
    
    public static void main(String[] args) {
        System.out.println("Java Multithreading Benchmark Suite");
        System.out.println("====================================");
        
        int availableProcessors = Runtime.getRuntime().availableProcessors();
        int threadCount = Math.max(4, availableProcessors);
        
        System.out.println("Available processors: " + availableProcessors);
        System.out.println("Using thread count: " + threadCount);
        System.out.println();
        
        List<ThreadingResult> results = new ArrayList<>();
        
        // Run multithreading benchmarks
        results.add(parallelMatrixMultiplication(threadCount));
        System.out.println();
        
        results.add(parallelPrimeCalculation(threadCount));
        System.out.println();
        
        results.add(producerConsumerTest(threadCount));
        System.out.println();
        
        results.add(parallelSorting(threadCount));
        System.out.println();
        
        results.add(concurrentMapOperations(threadCount));
        System.out.println();
        
        // Print threading performance summary
        System.out.println("=== MULTITHREADING PERFORMANCE SUMMARY ===");
        System.out.printf("%-30s %12s %12s %12s %12s%n", 
            "Test", "Single(ms)", "Multi(ms)", "Speedup", "Efficiency");
        System.out.println("-".repeat(80));
        
        for (ThreadingResult result : results) {
            System.out.printf("%-30s %12d %12d %12.2fx %12.2f%%%n",
                result.testName,
                result.singleThreadTime,
                result.multiThreadTime,
                result.speedup,
                result.efficiency * 100);
        }
        
        System.out.println();
        double avgSpeedup = results.stream().mapToDouble(r -> r.speedup).average().orElse(0);
        double avgEfficiency = results.stream().mapToDouble(r -> r.efficiency).average().orElse(0);
        
        System.out.printf("Average Speedup: %.2fx%n", avgSpeedup);
        System.out.printf("Average Efficiency: %.2f%%%n", avgEfficiency * 100);
    }
}
