package benchmark;

import java.util.*;

public class JITPhasesBenchmark {
    
    public static class JITPhaseResult {
        public String testName;
        public long[] times;
        public String[] phases;
        public double interpretedPhase;    // Iterations 1-2
        public double tier1Phase;         // Iterations 3-5  
        public double tier2Phase;         // Iterations 6-8
        public double fullyOptimizedPhase; // Iterations 9-15
        public double overallImprovement;
        
        public JITPhaseResult(String testName, long[] times) {
            this.testName = testName;
            this.times = times;
            this.phases = new String[times.length];
            calculatePhases();
        }
        
        private void calculatePhases() {
            // Phase classification based on JIT compilation stages
            for (int i = 0; i < times.length; i++) {
                if (i < 2) {
                    phases[i] = "INTERPRETED";
                } else if (i < 5) {
                    phases[i] = "TIER1_C1";
                } else if (i < 8) {
                    phases[i] = "TIER2_C2";
                } else {
                    phases[i] = "OPTIMIZED";
                }
            }
            
            // Calculate average performance for each phase
            interpretedPhase = calculateAverage(0, 2);
            tier1Phase = calculateAverage(2, 5);
            tier2Phase = calculateAverage(5, 8);
            fullyOptimizedPhase = calculateAverage(8, Math.min(15, times.length));
            
            overallImprovement = interpretedPhase / fullyOptimizedPhase;
        }
        
        private double calculateAverage(int start, int end) {
            if (start >= times.length) return 0;
            end = Math.min(end, times.length);
            long sum = 0;
            int count = 0;
            for (int i = start; i < end; i++) {
                sum += times[i];
                count++;
            }
            return count > 0 ? (double)sum / count : 0;
        }
    }
    
    // Extended Matrix Multiplication with more iterations to see JIT phases
    public static JITPhaseResult matrixMultiplicationJIT(int iterations, int matrixSize, int innerLoops) {
        System.out.println("=== Matrix Multiplication JIT Phases Analysis ===");
        System.out.println("Matrix size: " + matrixSize + "x" + matrixSize + ", Inner loops: " + innerLoops);
        System.out.println("Tracking JIT compilation phases across " + iterations + " iterations");
        
        double[][] A = new double[matrixSize][matrixSize];
        double[][] B = new double[matrixSize][matrixSize];
        double[][] C = new double[matrixSize][matrixSize];
        
        // Initialize matrices
        Random random = new Random(42); // Fixed seed for consistency
        for (int i = 0; i < matrixSize; i++) {
            for (int j = 0; j < matrixSize; j++) {
                A[i][j] = random.nextDouble();
                B[i][j] = random.nextDouble();
            }
        }
        
        long[] times = new long[iterations];
        
        for (int t = 0; t < iterations; t++) {
            // Reset result matrix
            for (int i = 0; i < matrixSize; i++) {
                for (int j = 0; j < matrixSize; j++) {
                    C[i][j] = 0.0;
                }
            }
            
            // Force GC before each iteration for cleaner measurement
            if (t % 3 == 0) {
                System.gc();
                try { Thread.sleep(10); } catch (InterruptedException e) {}
            }
            
            long start = System.nanoTime();
            
            for (int loop = 0; loop < innerLoops; loop++) {
                for (int i = 0; i < matrixSize; i++) {
                    for (int k = 0; k < matrixSize; k++) {
                        double aik = A[i][k];
                        for (int j = 0; j < matrixSize; j++) {
                            C[i][j] += aik * B[k][j];
                        }
                    }
                }
            }
            
            long end = System.nanoTime();
            times[t] = (end - start) / 1_000_000; // Convert to ms
            
            String phase = getJITPhase(t);
            System.out.printf("Iteration %2d (%s): %6d ms%n", t + 1, phase, times[t]);
        }
        
        return new JITPhaseResult("Matrix Multiplication JIT", times);
    }
    
    // Complex Algorithm with JIT tracking
    public static JITPhaseResult complexAlgorithmJIT(int iterations) {
        System.out.println("=== Complex Algorithm JIT Phases Analysis ===");
        System.out.println("Fibonacci + Prime + Mathematical operations combined");
        
        long[] times = new long[iterations];
        
        for (int t = 0; t < iterations; t++) {
            if (t % 3 == 0) {
                System.gc();
                try { Thread.sleep(10); } catch (InterruptedException e) {}
            }
            
            long start = System.nanoTime();
            
            // Complex computation combining multiple algorithms
            long fibResult = 0;
            int primeCount = 0;
            double mathResult = 0.0;
            
            // Fibonacci calculation
            for (int n = 1; n <= 100000; n++) {
                fibResult += fibonacciIterative(n % 45); // Prevent overflow
            }
            
            // Prime calculation
            for (int num = 2; num <= 10000; num++) {
                if (isPrime(num)) {
                    primeCount++;
                }
            }
            
            // Mathematical operations
            Random random = new Random(42);
            for (int i = 0; i < 500000; i++) {
                double x = random.nextDouble() * 100;
                mathResult += Math.sin(x) * Math.cos(x) + Math.sqrt(x) + Math.log(x + 1);
            }
            
            long end = System.nanoTime();
            times[t] = (end - start) / 1_000_000;
            
            String phase = getJITPhase(t);
            System.out.printf("Iteration %2d (%s): %6d ms (fib: %d, primes: %d, math: %.2f)%n", 
                t + 1, phase, times[t], fibResult % 10000, primeCount, mathResult / 1000000);
        }
        
        return new JITPhaseResult("Complex Algorithm JIT", times);
    }
    
    // String processing with JIT tracking
    public static JITPhaseResult stringProcessingJIT(int iterations) {
        System.out.println("=== String Processing JIT Phases Analysis ===");
        
        long[] times = new long[iterations];
        
        for (int t = 0; t < iterations; t++) {
            if (t % 3 == 0) {
                System.gc();
                try { Thread.sleep(10); } catch (InterruptedException e) {}
            }
            
            long start = System.nanoTime();
            
            // String operations that benefit from JIT optimization
            StringBuilder sb = new StringBuilder();
            List<String> strings = new ArrayList<>();
            
            // Generate strings
            for (int i = 0; i < 50000; i++) {
                String str = "String_" + i + "_with_some_content_" + (i * 17) % 1000;
                strings.add(str);
                sb.append(str).append(" ");
            }
            
            // Process strings
            int totalLength = 0;
            int upperCaseCount = 0;
            for (String str : strings) {
                totalLength += str.length();
                String upper = str.toUpperCase();
                if (upper.contains("CONTENT")) {
                    upperCaseCount++;
                }
            }
            
            String result = sb.toString();
            
            long end = System.nanoTime();
            times[t] = (end - start) / 1_000_000;
            
            String phase = getJITPhase(t);
            System.out.printf("Iteration %2d (%s): %6d ms (strings: %d, total len: %d, upper: %d)%n", 
                t + 1, phase, times[t], strings.size(), totalLength, upperCaseCount);
        }
        
        return new JITPhaseResult("String Processing JIT", times);
    }
    
    private static String getJITPhase(int iteration) {
        if (iteration < 2) {
            return "INTERPRETED ";
        } else if (iteration < 5) {
            return "TIER1_C1   ";
        } else if (iteration < 8) {
            return "TIER2_C2   ";
        } else {
            return "OPTIMIZED  ";
        }
    }
    
    private static long fibonacciIterative(int n) {
        if (n <= 1) return n;
        long a = 0, b = 1;
        for (int i = 2; i <= n; i++) {
            long temp = a + b;
            a = b;
            b = temp;
        }
        return b;
    }
    
    private static boolean isPrime(int n) {
        if (n < 2) return false;
        if (n == 2) return true;
        if (n % 2 == 0) return false;
        
        for (int i = 3; i * i <= n; i += 2) {
            if (n % i == 0) return false;
        }
        return true;
    }
    
    public static void main(String[] args) {
        System.out.println("Java JIT Compilation Phases Analysis");
        System.out.println("=====================================");
        System.out.println("Tracking performance across JIT compilation phases:");
        System.out.println("- INTERPRETED: Initial runs, bytecode interpretation");
        System.out.println("- TIER1_C1: Client compiler, fast compilation, basic optimizations");
        System.out.println("- TIER2_C2: Server compiler, slow compilation, aggressive optimizations");
        System.out.println("- OPTIMIZED: Fully optimized with profile-guided optimizations");
        System.out.println();
        
        List<JITPhaseResult> results = new ArrayList<>();
        
        // Run extended benchmarks with more iterations
        results.add(matrixMultiplicationJIT(15, 100, 1000));
        System.out.println();
        
        results.add(complexAlgorithmJIT(15));
        System.out.println();
        
        results.add(stringProcessingJIT(15));
        System.out.println();
        
        // Print detailed JIT phase analysis
        System.out.println("=== JIT PHASES ANALYSIS ===");
        for (JITPhaseResult result : results) {
            System.out.printf("%-30s:%n", result.testName);
            System.out.printf("  Interpreted Phase (1-2):     %8.2f ms%n", result.interpretedPhase);
            System.out.printf("  Tier1 C1 Phase (3-5):        %8.2f ms%n", result.tier1Phase);
            System.out.printf("  Tier2 C2 Phase (6-8):        %8.2f ms%n", result.tier2Phase);
            System.out.printf("  Fully Optimized (9-15):      %8.2f ms%n", result.fullyOptimizedPhase);
            System.out.printf("  Overall Improvement:         %8.2fx%n", result.overallImprovement);
            System.out.println();
        }
        
        // Summary comparison
        System.out.println("=== PHASE COMPARISON SUMMARY ===");
        System.out.printf("%-30s %12s %12s %12s %12s %12s%n", 
            "Test", "Interpreted", "Tier1_C1", "Tier2_C2", "Optimized", "Improvement");
        System.out.println("-".repeat(100));
        
        for (JITPhaseResult result : results) {
            System.out.printf("%-30s %12.2f %12.2f %12.2f %12.2f %12.2fx%n",
                result.testName,
                result.interpretedPhase,
                result.tier1Phase,
                result.tier2Phase,
                result.fullyOptimizedPhase,
                result.overallImprovement);
        }
    }
}
