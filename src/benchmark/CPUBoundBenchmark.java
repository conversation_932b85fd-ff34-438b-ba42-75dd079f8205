package benchmark;

import java.util.ArrayList;
import java.util.List;

public class CPUBoundBenchmark {
    
    public static class BenchmarkResult {
        public String testName;
        public long[] times;
        public double averageTime;
        public double improvementRatio;
        
        public BenchmarkResult(String testName, long[] times) {
            this.testName = testName;
            this.times = times;
            this.averageTime = calculateAverage(times);
            this.improvementRatio = times.length > 1 ? (double)times[0] / times[times.length - 1] : 1.0;
        }
        
        private double calculateAverage(long[] times) {
            long sum = 0;
            for (long time : times) sum += time;
            return (double)sum / times.length;
        }
    }
    
    // Test 1: Matrix Multiplication (CPU intensive)
    public static BenchmarkResult matrixMultiplication(int iterations, int matrixSize, int innerLoops) {
        System.out.println("=== Matrix Multiplication Benchmark ===");
        System.out.println("Matrix size: " + matrixSize + "x" + matrixSize + ", Inner loops: " + innerLoops);
        
        double[][] A = new double[matrixSize][matrixSize];
        double[][] B = new double[matrixSize][matrixSize];
        double[][] C = new double[matrixSize][matrixSize];
        
        // Initialize matrices
        for (int i = 0; i < matrixSize; i++) {
            for (int j = 0; j < matrixSize; j++) {
                A[i][j] = Math.random();
                B[i][j] = Math.random();
            }
        }
        
        long[] times = new long[iterations];
        
        for (int t = 0; t < iterations; t++) {
            // Reset result matrix
            for (int i = 0; i < matrixSize; i++) {
                for (int j = 0; j < matrixSize; j++) {
                    C[i][j] = 0.0;
                }
            }
            
            long start = System.nanoTime();
            
            for (int loop = 0; loop < innerLoops; loop++) {
                for (int i = 0; i < matrixSize; i++) {
                    for (int k = 0; k < matrixSize; k++) {
                        double aik = A[i][k];
                        for (int j = 0; j < matrixSize; j++) {
                            C[i][j] += aik * B[k][j];
                        }
                    }
                }
            }
            
            long end = System.nanoTime();
            times[t] = (end - start) / 1_000_000; // Convert to ms
            System.out.printf("Iteration %d: %d ms%n", t + 1, times[t]);
        }
        
        return new BenchmarkResult("Matrix Multiplication", times);
    }
    
    // Test 2: Prime Number Calculation (CPU intensive)
    public static BenchmarkResult primeCalculation(int iterations, int maxNumber) {
        System.out.println("=== Prime Number Calculation Benchmark ===");
        System.out.println("Finding primes up to: " + maxNumber);
        
        long[] times = new long[iterations];
        
        for (int t = 0; t < iterations; t++) {
            long start = System.nanoTime();
            
            List<Integer> primes = new ArrayList<>();
            for (int num = 2; num <= maxNumber; num++) {
                if (isPrime(num)) {
                    primes.add(num);
                }
            }
            
            long end = System.nanoTime();
            times[t] = (end - start) / 1_000_000;
            System.out.printf("Iteration %d: %d ms (found %d primes)%n", t + 1, times[t], primes.size());
        }
        
        return new BenchmarkResult("Prime Calculation", times);
    }
    
    private static boolean isPrime(int n) {
        if (n < 2) return false;
        if (n == 2) return true;
        if (n % 2 == 0) return false;
        
        for (int i = 3; i * i <= n; i += 2) {
            if (n % i == 0) return false;
        }
        return true;
    }
    
    // Test 3: Mathematical Operations (CPU intensive)
    public static BenchmarkResult mathematicalOperations(int iterations, int operationCount) {
        System.out.println("=== Mathematical Operations Benchmark ===");
        System.out.println("Operations count: " + operationCount);
        
        long[] times = new long[iterations];
        
        for (int t = 0; t < iterations; t++) {
            long start = System.nanoTime();
            
            double result = 0.0;
            for (int i = 0; i < operationCount; i++) {
                double x = Math.random() * 100;
                result += Math.sin(x) * Math.cos(x) + Math.sqrt(x) + Math.log(x + 1) + Math.pow(x, 0.5);
            }
            
            long end = System.nanoTime();
            times[t] = (end - start) / 1_000_000;
            System.out.printf("Iteration %d: %d ms (result: %.6f)%n", t + 1, times[t], result);
        }
        
        return new BenchmarkResult("Mathematical Operations", times);
    }
    
    // Test 4: Fibonacci Calculation (CPU intensive with recursion)
    public static BenchmarkResult fibonacciCalculation(int iterations, int fibNumber) {
        System.out.println("=== Fibonacci Calculation Benchmark ===");
        System.out.println("Calculating fibonacci(" + fibNumber + ")");
        
        long[] times = new long[iterations];
        
        for (int t = 0; t < iterations; t++) {
            long start = System.nanoTime();
            
            long result = fibonacciIterative(fibNumber);
            
            long end = System.nanoTime();
            times[t] = (end - start) / 1_000_000;
            System.out.printf("Iteration %d: %d ms (fib(%d) = %d)%n", t + 1, times[t], fibNumber, result);
        }
        
        return new BenchmarkResult("Fibonacci Calculation", times);
    }
    
    private static long fibonacciIterative(int n) {
        if (n <= 1) return n;
        long a = 0, b = 1;
        for (int i = 2; i <= n; i++) {
            long temp = a + b;
            a = b;
            b = temp;
        }
        return b;
    }
    
    public static void main(String[] args) {
        System.out.println("Java CPU-Bound Benchmark Suite");
        System.out.println("==============================");
        
        List<BenchmarkResult> results = new ArrayList<>();
        
        // Run benchmarks
        results.add(matrixMultiplication(10, 100, 1000));
        System.out.println();
        
        results.add(primeCalculation(10, 50000));
        System.out.println();
        
        results.add(mathematicalOperations(10, 1000000));
        System.out.println();
        
        results.add(fibonacciCalculation(10, 1000000));
        System.out.println();
        
        // Print summary
        System.out.println("=== SUMMARY ===");
        for (BenchmarkResult result : results) {
            System.out.printf("%-25s: First: %6d ms, Last: %6d ms, Avg: %8.2f ms, Improvement: %.2fx%n",
                result.testName, result.times[0], result.times[result.times.length - 1], 
                result.averageTime, result.improvementRatio);
        }
    }
}
