package benchmark;

import java.util.*;

public class MemoryBoundBenchmark {
    
    public static class BenchmarkResult {
        public String testName;
        public long[] times;
        public double averageTime;
        public double improvementRatio;
        
        public BenchmarkResult(String testName, long[] times) {
            this.testName = testName;
            this.times = times;
            this.averageTime = calculateAverage(times);
            this.improvementRatio = times.length > 1 ? (double)times[0] / times[times.length - 1] : 1.0;
        }
        
        private double calculateAverage(long[] times) {
            long sum = 0;
            for (long time : times) sum += time;
            return (double)sum / times.length;
        }
    }
    
    // Test 1: Large Array Operations
    public static BenchmarkResult largeArrayOperations(int iterations, int arraySize) {
        System.out.println("=== Large Array Operations Benchmark ===");
        System.out.println("Array size: " + arraySize + " elements");
        
        long[] times = new long[iterations];
        
        for (int t = 0; t < iterations; t++) {
            long start = System.nanoTime();
            
            // Create large arrays
            int[] array1 = new int[arraySize];
            int[] array2 = new int[arraySize];
            int[] result = new int[arraySize];
            
            // Fill arrays with random data
            Random random = new Random();
            for (int i = 0; i < arraySize; i++) {
                array1[i] = random.nextInt(1000);
                array2[i] = random.nextInt(1000);
            }
            
            // Perform operations
            for (int i = 0; i < arraySize; i++) {
                result[i] = array1[i] + array2[i] * 2 - array1[i] / 3;
            }
            
            // Sum to prevent optimization
            long sum = 0;
            for (int value : result) {
                sum += value;
            }
            
            long end = System.nanoTime();
            times[t] = (end - start) / 1_000_000;
            System.out.printf("Iteration %d: %d ms (sum: %d)%n", t + 1, times[t], sum);
        }
        
        return new BenchmarkResult("Large Array Operations", times);
    }
    
    // Test 2: Memory Allocation/Deallocation
    public static BenchmarkResult memoryAllocation(int iterations, int objectCount) {
        System.out.println("=== Memory Allocation Benchmark ===");
        System.out.println("Object count: " + objectCount);
        
        long[] times = new long[iterations];
        
        for (int t = 0; t < iterations; t++) {
            long start = System.nanoTime();
            
            List<double[]> objects = new ArrayList<>();
            
            // Allocate many objects
            for (int i = 0; i < objectCount; i++) {
                double[] array = new double[1000];
                for (int j = 0; j < array.length; j++) {
                    array[j] = Math.random();
                }
                objects.add(array);
            }
            
            // Process objects
            double totalSum = 0;
            for (double[] array : objects) {
                for (double value : array) {
                    totalSum += value;
                }
            }
            
            // Clear objects (help GC)
            objects.clear();
            
            long end = System.nanoTime();
            times[t] = (end - start) / 1_000_000;
            System.out.printf("Iteration %d: %d ms (sum: %.2f)%n", t + 1, times[t], totalSum);
        }
        
        return new BenchmarkResult("Memory Allocation", times);
    }
    
    // Test 3: Cache-unfriendly Memory Access
    public static BenchmarkResult cacheUnfriendlyAccess(int iterations, int matrixSize) {
        System.out.println("=== Cache-unfriendly Memory Access Benchmark ===");
        System.out.println("Matrix size: " + matrixSize + "x" + matrixSize);
        
        long[] times = new long[iterations];
        
        for (int t = 0; t < iterations; t++) {
            // Create large matrix
            int[][] matrix = new int[matrixSize][matrixSize];
            Random random = new Random();
            
            // Fill matrix
            for (int i = 0; i < matrixSize; i++) {
                for (int j = 0; j < matrixSize; j++) {
                    matrix[i][j] = random.nextInt(100);
                }
            }
            
            long start = System.nanoTime();
            
            // Column-wise access (cache-unfriendly)
            long sum = 0;
            for (int j = 0; j < matrixSize; j++) {
                for (int i = 0; i < matrixSize; i++) {
                    sum += matrix[i][j];
                }
            }
            
            long end = System.nanoTime();
            times[t] = (end - start) / 1_000_000;
            System.out.printf("Iteration %d: %d ms (sum: %d)%n", t + 1, times[t], sum);
        }
        
        return new BenchmarkResult("Cache-unfriendly Access", times);
    }
    
    // Test 4: String Operations (Memory intensive)
    public static BenchmarkResult stringOperations(int iterations, int stringCount) {
        System.out.println("=== String Operations Benchmark ===");
        System.out.println("String count: " + stringCount);
        
        long[] times = new long[iterations];
        
        for (int t = 0; t < iterations; t++) {
            long start = System.nanoTime();
            
            List<String> strings = new ArrayList<>();
            StringBuilder sb = new StringBuilder();
            
            // Create many strings
            for (int i = 0; i < stringCount; i++) {
                String str = "String number " + i + " with some additional text to make it longer";
                strings.add(str);
                sb.append(str).append(" ");
            }
            
            // Process strings
            int totalLength = 0;
            for (String str : strings) {
                totalLength += str.length();
                str.toUpperCase(); // Create new string objects
            }
            
            String result = sb.toString();
            
            long end = System.nanoTime();
            times[t] = (end - start) / 1_000_000;
            System.out.printf("Iteration %d: %d ms (total length: %d, result length: %d)%n", 
                t + 1, times[t], totalLength, result.length());
        }
        
        return new BenchmarkResult("String Operations", times);
    }
    
    // Test 5: Collection Operations
    public static BenchmarkResult collectionOperations(int iterations, int elementCount) {
        System.out.println("=== Collection Operations Benchmark ===");
        System.out.println("Element count: " + elementCount);
        
        long[] times = new long[iterations];
        
        for (int t = 0; t < iterations; t++) {
            long start = System.nanoTime();
            
            // Test different collections
            List<Integer> list = new ArrayList<>();
            Set<Integer> set = new HashSet<>();
            Map<Integer, String> map = new HashMap<>();
            
            Random random = new Random();
            
            // Fill collections
            for (int i = 0; i < elementCount; i++) {
                int value = random.nextInt(elementCount / 2); // Some duplicates
                list.add(value);
                set.add(value);
                map.put(value, "Value " + value);
            }
            
            // Operations
            Collections.sort(list);
            
            int sum = 0;
            for (Integer value : set) {
                sum += value;
            }
            
            for (Map.Entry<Integer, String> entry : map.entrySet()) {
                entry.getValue().length(); // Access string
            }
            
            long end = System.nanoTime();
            times[t] = (end - start) / 1_000_000;
            System.out.printf("Iteration %d: %d ms (list: %d, set: %d, map: %d, sum: %d)%n", 
                t + 1, times[t], list.size(), set.size(), map.size(), sum);
        }
        
        return new BenchmarkResult("Collection Operations", times);
    }
    
    public static void main(String[] args) {
        System.out.println("Java Memory-Bound Benchmark Suite");
        System.out.println("==================================");
        
        List<BenchmarkResult> results = new ArrayList<>();
        
        // Run benchmarks
        results.add(largeArrayOperations(10, 10_000_000));
        System.out.println();
        
        results.add(memoryAllocation(10, 10_000));
        System.out.println();
        
        results.add(cacheUnfriendlyAccess(10, 2000));
        System.out.println();
        
        results.add(stringOperations(10, 100_000));
        System.out.println();
        
        results.add(collectionOperations(10, 500_000));
        System.out.println();
        
        // Print summary
        System.out.println("=== SUMMARY ===");
        for (BenchmarkResult result : results) {
            System.out.printf("%-25s: First: %6d ms, Last: %6d ms, Avg: %8.2f ms, Improvement: %.2fx%n",
                result.testName, result.times[0], result.times[result.times.length - 1], 
                result.averageTime, result.improvementRatio);
        }
    }
}
