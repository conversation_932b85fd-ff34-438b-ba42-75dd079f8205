#include <iostream>
#include <fstream>
#include <vector>
#include <chrono>
#include <random>
#include <string>
#include <filesystem>
#include <sstream>
#include <iomanip>

namespace fs = std::filesystem;

class BenchmarkResult {
public:
    std::string testName;
    std::vector<long long> times;
    double averageTime;
    double improvementRatio;
    
    BenchmarkResult(const std::string& name, const std::vector<long long>& t) 
        : testName(name), times(t) {
        calculateStats();
    }
    
private:
    void calculateStats() {
        long long sum = 0;
        for (long long time : times) sum += time;
        averageTime = (double)sum / times.size();
        improvementRatio = times.size() > 1 ? (double)times[0] / times[times.size() - 1] : 1.0;
    }
};

// Test 1: File Writing and Reading
BenchmarkResult fileOperations(int iterations, int fileCount, int fileSize) {
    std::cout << "=== File Operations Benchmark ===" << std::endl;
    std::cout << "File count: " << fileCount << ", File size: " << fileSize << " bytes" << std::endl;
    
    std::vector<long long> times(iterations);
    std::string tempDir = "temp_benchmark";
    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);
    
    for (int t = 0; t < iterations; t++) {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            // Create temp directory
            fs::create_directory(tempDir);
            
            // Generate test data
            std::vector<char> data(fileSize);
            for (int i = 0; i < fileSize; i++) {
                data[i] = static_cast<char>(dis(gen));
            }
            
            // Write files
            std::vector<std::string> filePaths;
            for (int i = 0; i < fileCount; i++) {
                std::string filePath = tempDir + "/test_file_" + std::to_string(i) + ".dat";
                std::ofstream file(filePath, std::ios::binary);
                file.write(data.data(), fileSize);
                file.close();
                filePaths.push_back(filePath);
            }
            
            // Read files back
            long long totalBytesRead = 0;
            for (const auto& filePath : filePaths) {
                std::ifstream file(filePath, std::ios::binary);
                std::vector<char> readData(fileSize);
                file.read(readData.data(), fileSize);
                totalBytesRead += file.gcount();
                file.close();
            }
            
            // Cleanup
            for (const auto& filePath : filePaths) {
                fs::remove(filePath);
            }
            fs::remove(tempDir);
            
            auto end = std::chrono::high_resolution_clock::now();
            times[t] = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
            std::cout << "Iteration " << (t + 1) << ": " << times[t] << " ms (bytes read: " << totalBytesRead << ")" << std::endl;
            
        } catch (const std::exception& e) {
            std::cerr << "IO Error: " << e.what() << std::endl;
            times[t] = -1;
        }
    }
    
    return BenchmarkResult("File Operations", times);
}

// Test 2: CSV File Processing
BenchmarkResult csvProcessing(int iterations, int recordCount) {
    std::cout << "=== CSV Processing Benchmark ===" << std::endl;
    std::cout << "Record count: " << recordCount << std::endl;
    
    std::vector<long long> times(iterations);
    std::string csvFile = "test_data.csv";
    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> ageDis(20, 60);
    std::uniform_real_distribution<> salaryDis(30000, 100000);
    std::uniform_int_distribution<> deptDis(0, 9);
    
    for (int t = 0; t < iterations; t++) {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            // Generate CSV data
            std::ofstream writer(csvFile);
            writer << "id,name,age,salary,department\n";
            for (int i = 0; i < recordCount; i++) {
                writer << i << ",Employee_" << i << "," << ageDis(gen) << "," 
                       << std::fixed << std::setprecision(2) << salaryDis(gen) 
                       << ",Dept_" << deptDis(gen) << "\n";
            }
            writer.close();
            
            // Read and process CSV
            std::vector<std::vector<std::string>> records;
            std::ifstream reader(csvFile);
            std::string line;
            std::getline(reader, line); // Skip header
            
            while (std::getline(reader, line)) {
                std::vector<std::string> fields;
                std::stringstream ss(line);
                std::string field;
                
                while (std::getline(ss, field, ',')) {
                    fields.push_back(field);
                }
                records.push_back(fields);
            }
            reader.close();
            
            // Process data
            double totalSalary = 0;
            int totalAge = 0;
            for (const auto& record : records) {
                if (record.size() >= 4) {
                    totalAge += std::stoi(record[2]);
                    totalSalary += std::stod(record[3]);
                }
            }
            
            // Cleanup
            fs::remove(csvFile);
            
            auto end = std::chrono::high_resolution_clock::now();
            times[t] = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
            std::cout << "Iteration " << (t + 1) << ": " << times[t] << " ms (records: " << records.size() 
                     << ", avg age: " << std::fixed << std::setprecision(1) << (double)totalAge / records.size()
                     << ", avg salary: " << std::setprecision(2) << totalSalary / records.size() << ")" << std::endl;
            
        } catch (const std::exception& e) {
            std::cerr << "IO Error: " << e.what() << std::endl;
            times[t] = -1;
        }
    }
    
    return BenchmarkResult("CSV Processing", times);
}

// Test 3: Directory Operations
BenchmarkResult directoryOperations(int iterations, int dirCount, int filesPerDir) {
    std::cout << "=== Directory Operations Benchmark ===" << std::endl;
    std::cout << "Directory count: " << dirCount << ", Files per directory: " << filesPerDir << std::endl;
    
    std::vector<long long> times(iterations);
    std::string baseDir = "benchmark_dirs";
    
    for (int t = 0; t < iterations; t++) {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            fs::create_directory(baseDir);
            std::vector<std::string> createdPaths;
            
            // Create directories and files
            for (int d = 0; d < dirCount; d++) {
                std::string dirPath = baseDir + "/dir_" + std::to_string(d);
                fs::create_directory(dirPath);
                createdPaths.push_back(dirPath);
                
                for (int f = 0; f < filesPerDir; f++) {
                    std::string filePath = dirPath + "/file_" + std::to_string(f) + ".txt";
                    std::ofstream file(filePath);
                    file << "Content of file " << f << " in directory " << d;
                    file.close();
                    createdPaths.push_back(filePath);
                }
            }
            
            // List and count files
            int totalFiles = 0;
            for (const auto& entry : fs::directory_iterator(baseDir)) {
                if (entry.is_directory()) {
                    for (const auto& fileEntry : fs::directory_iterator(entry.path())) {
                        if (fileEntry.is_regular_file()) {
                            totalFiles++;
                        }
                    }
                }
            }
            
            // Cleanup
            fs::remove_all(baseDir);
            
            auto end = std::chrono::high_resolution_clock::now();
            times[t] = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
            std::cout << "Iteration " << (t + 1) << ": " << times[t] << " ms (total files: " << totalFiles << ")" << std::endl;
            
        } catch (const std::exception& e) {
            std::cerr << "IO Error: " << e.what() << std::endl;
            times[t] = -1;
        }
    }
    
    return BenchmarkResult("Directory Operations", times);
}

// Test 4: Binary Data Operations
BenchmarkResult binaryDataOperations(int iterations, int dataSize) {
    std::cout << "=== Binary Data Operations Benchmark ===" << std::endl;
    std::cout << "Data size: " << dataSize << " bytes" << std::endl;
    
    std::vector<long long> times(iterations);
    std::string binFile = "binary_data.bin";
    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);
    
    for (int t = 0; t < iterations; t++) {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            // Generate binary data
            std::vector<char> data(dataSize);
            for (int i = 0; i < dataSize; i++) {
                data[i] = static_cast<char>(dis(gen));
            }
            
            // Write binary data
            std::ofstream writer(binFile, std::ios::binary);
            writer.write(data.data(), dataSize);
            writer.close();
            
            // Read binary data
            std::ifstream reader(binFile, std::ios::binary);
            std::vector<char> readData(dataSize);
            reader.read(readData.data(), dataSize);
            reader.close();
            
            // Process data (simple checksum)
            long long checksum = 0;
            for (char byte : readData) {
                checksum += static_cast<unsigned char>(byte);
            }
            
            // Cleanup
            fs::remove(binFile);
            
            auto end = std::chrono::high_resolution_clock::now();
            times[t] = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
            std::cout << "Iteration " << (t + 1) << ": " << times[t] << " ms (checksum: " << checksum << ")" << std::endl;
            
        } catch (const std::exception& e) {
            std::cerr << "IO Error: " << e.what() << std::endl;
            times[t] = -1;
        }
    }
    
    return BenchmarkResult("Binary Data Operations", times);
}

int main() {
    std::cout << "C++ I/O-Bound Benchmark Suite" << std::endl;
    std::cout << "===============================" << std::endl;
    
    std::vector<BenchmarkResult> results;
    
    // Run benchmarks
    results.push_back(fileOperations(10, 100, 10240)); // 100 files, 10KB each
    std::cout << std::endl;
    
    results.push_back(csvProcessing(10, 50000)); // 50K records
    std::cout << std::endl;
    
    results.push_back(directoryOperations(10, 50, 20)); // 50 dirs, 20 files each
    std::cout << std::endl;
    
    results.push_back(binaryDataOperations(10, 1048576)); // 1MB binary data
    std::cout << std::endl;
    
    // Print summary
    std::cout << "=== SUMMARY ===" << std::endl;
    for (const auto& result : results) {
        std::cout << std::left << std::setw(25) << result.testName << ": "
                  << "First: " << std::setw(6) << result.times[0] << " ms, "
                  << "Last: " << std::setw(6) << result.times[result.times.size() - 1] << " ms, "
                  << "Avg: " << std::setw(8) << std::fixed << std::setprecision(2) << result.averageTime << " ms, "
                  << "Improvement: " << std::setprecision(2) << result.improvementRatio << "x" << std::endl;
    }
    
    return 0;
}
