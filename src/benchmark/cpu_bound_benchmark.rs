use rand::Rng;
use std::time::Instant;

struct Ben<PERSON><PERSON><PERSON><PERSON>ult {
    test_name: String,
    times: Vec<u128>,
    average_time: f64,
    improvement_ratio: f64,
}

impl BenchmarkResult {
    fn new(test_name: String, times: Vec<u128>) -> Self {
        let average_time = times.iter().sum::<u128>() as f64 / times.len() as f64;
        let improvement_ratio = if times.len() > 1 && times[times.len() - 1] > 0 {
            times[0] as f64 / times[times.len() - 1] as f64
        } else {
            1.0
        };
        
        BenchmarkResult {
            test_name,
            times,
            average_time,
            improvement_ratio,
        }
    }
}

fn matrix_multiplication(iterations: usize, matrix_size: usize, inner_loops: usize) -> BenchmarkResult {
    println!("=== Matrix Multiplication Benchmark ===");
    println!("Matrix size: {}x{}, Inner loops: {}", matrix_size, matrix_size, inner_loops);
    
    let mut times = Vec::new();
    let mut rng = rand::thread_rng();
    
    for t in 0..iterations {
        // Initialize matrices
        let mut a = vec![vec![0.0f64; matrix_size]; matrix_size];
        let mut b = vec![vec![0.0f64; matrix_size]; matrix_size];
        let mut c = vec![vec![0.0f64; matrix_size]; matrix_size];
        
        for i in 0..matrix_size {
            for j in 0..matrix_size {
                a[i][j] = rng.gen::<f64>();
                b[i][j] = rng.gen::<f64>();
            }
        }
        
        let start = Instant::now();
        
        for _ in 0..inner_loops {
            for i in 0..matrix_size {
                for k in 0..matrix_size {
                    let aik = a[i][k];
                    for j in 0..matrix_size {
                        c[i][j] += aik * b[k][j];
                    }
                }
            }
        }
        
        let elapsed = start.elapsed().as_millis();
        times.push(elapsed);
        println!("Iteration {}: {} ms", t + 1, elapsed);
    }
    
    BenchmarkResult::new("Matrix Multiplication".to_string(), times)
}

fn prime_calculation(iterations: usize, max_number: usize) -> BenchmarkResult {
    println!("=== Prime Number Calculation Benchmark ===");
    println!("Finding primes up to: {}", max_number);
    
    let mut times = Vec::new();
    
    fn is_prime(n: usize) -> bool {
        if n < 2 { return false; }
        if n == 2 { return true; }
        if n % 2 == 0 { return false; }
        
        let sqrt_n = (n as f64).sqrt() as usize;
        for i in (3..=sqrt_n).step_by(2) {
            if n % i == 0 {
                return false;
            }
        }
        true
    }
    
    for t in 0..iterations {
        let start = Instant::now();
        
        let mut primes = Vec::new();
        for num in 2..=max_number {
            if is_prime(num) {
                primes.push(num);
            }
        }
        
        let elapsed = start.elapsed().as_millis();
        times.push(elapsed);
        println!("Iteration {}: {} ms (found {} primes)", t + 1, elapsed, primes.len());
    }
    
    BenchmarkResult::new("Prime Calculation".to_string(), times)
}

fn mathematical_operations(iterations: usize, operation_count: usize) -> BenchmarkResult {
    println!("=== Mathematical Operations Benchmark ===");
    println!("Operations count: {}", operation_count);
    
    let mut times = Vec::new();
    let mut rng = rand::thread_rng();
    
    for t in 0..iterations {
        let start = Instant::now();
        
        let mut result = 0.0f64;
        for _ in 0..operation_count {
            let x: f64 = rng.gen::<f64>() * 100.0;
            result += x.sin() * x.cos() + x.sqrt() + (x + 1.0).ln() + x.powf(0.5);
        }
        
        let elapsed = start.elapsed().as_millis();
        times.push(elapsed);
        println!("Iteration {}: {} ms (result: {:.6})", t + 1, elapsed, result);
    }
    
    BenchmarkResult::new("Mathematical Operations".to_string(), times)
}

fn fibonacci_calculation(iterations: usize, fib_number: usize) -> BenchmarkResult {
    println!("=== Fibonacci Calculation Benchmark ===");
    println!("Calculating fibonacci({})", fib_number);
    
    let mut times = Vec::new();
    
    fn fibonacci_iterative(n: usize) -> u64 {
        if n <= 1 { return n as u64; }
        let mut a = 0u64;
        let mut b = 1u64;
        for _ in 2..=n {
            let temp = a.wrapping_add(b);
            a = b;
            b = temp;
        }
        b
    }
    
    for t in 0..iterations {
        let start = Instant::now();
        
        let result = fibonacci_iterative(fib_number);
        
        let elapsed = start.elapsed().as_millis();
        times.push(elapsed);
        println!("Iteration {}: {} ms (fib({}) = {})", t + 1, elapsed, fib_number, result as i64);
    }
    
    BenchmarkResult::new("Fibonacci Calculation".to_string(), times)
}

fn main() {
    println!("Rust CPU-Bound Benchmark Suite");
    println!("===============================");
    
    let mut results = Vec::new();
    
    // Run benchmarks
    results.push(matrix_multiplication(10, 100, 1000));
    println!();
    
    results.push(prime_calculation(10, 50000));
    println!();
    
    results.push(mathematical_operations(10, 1000000));
    println!();
    
    results.push(fibonacci_calculation(10, 1000000));
    println!();
    
    // Print summary
    println!("=== SUMMARY ===");
    for result in &results {
        println!("{:<25}: First: {:6} ms, Last: {:6} ms, Avg: {:8.2} ms, Improvement: {:.2}x",
            result.test_name,
            result.times[0],
            result.times[result.times.len() - 1],
            result.average_time,
            result.improvement_ratio
        );
    }
}
