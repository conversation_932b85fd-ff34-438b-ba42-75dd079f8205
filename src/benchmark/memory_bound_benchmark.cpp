#include <iostream>
#include <vector>
#include <chrono>
#include <random>
#include <algorithm>
#include <string>
#include <unordered_set>
#include <unordered_map>
#include <iomanip>

class BenchmarkResult {
public:
    std::string testName;
    std::vector<long long> times;
    double averageTime;
    double improvementRatio;
    
    BenchmarkResult(const std::string& name, const std::vector<long long>& t) 
        : testName(name), times(t) {
        calculateStats();
    }
    
private:
    void calculateStats() {
        long long sum = 0;
        for (long long time : times) sum += time;
        averageTime = (double)sum / times.size();
        improvementRatio = times.size() > 1 ? (double)times[0] / times[times.size() - 1] : 1.0;
    }
};

// Test 1: Large Array Operations
BenchmarkResult largeArrayOperations(int iterations, int arraySize) {
    std::cout << "=== Large Array Operations Benchmark ===" << std::endl;
    std::cout << "Array size: " << arraySize << " elements" << std::endl;
    
    std::vector<long long> times(iterations);
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 999);
    
    for (int t = 0; t < iterations; t++) {
        auto start = std::chrono::high_resolution_clock::now();
        
        // Create large arrays
        std::vector<int> array1(arraySize);
        std::vector<int> array2(arraySize);
        std::vector<int> result(arraySize);
        
        // Fill arrays with random data
        for (int i = 0; i < arraySize; i++) {
            array1[i] = dis(gen);
            array2[i] = dis(gen);
        }
        
        // Perform operations
        for (int i = 0; i < arraySize; i++) {
            result[i] = array1[i] + array2[i] * 2 - array1[i] / 3;
        }
        
        // Sum to prevent optimization
        long long sum = 0;
        for (int value : result) {
            sum += value;
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        times[t] = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
        std::cout << "Iteration " << (t + 1) << ": " << times[t] << " ms (sum: " << sum << ")" << std::endl;
    }
    
    return BenchmarkResult("Large Array Operations", times);
}

// Test 2: Memory Allocation/Deallocation
BenchmarkResult memoryAllocation(int iterations, int objectCount) {
    std::cout << "=== Memory Allocation Benchmark ===" << std::endl;
    std::cout << "Object count: " << objectCount << std::endl;
    
    std::vector<long long> times(iterations);
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<> dis(0.0, 1.0);
    
    for (int t = 0; t < iterations; t++) {
        auto start = std::chrono::high_resolution_clock::now();
        
        std::vector<std::vector<double>> objects;
        objects.reserve(objectCount);
        
        // Allocate many objects
        for (int i = 0; i < objectCount; i++) {
            std::vector<double> array(1000);
            for (int j = 0; j < 1000; j++) {
                array[j] = dis(gen);
            }
            objects.push_back(std::move(array));
        }
        
        // Process objects
        double totalSum = 0;
        for (const auto& array : objects) {
            for (double value : array) {
                totalSum += value;
            }
        }
        
        // Clear objects
        objects.clear();
        
        auto end = std::chrono::high_resolution_clock::now();
        times[t] = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
        std::cout << "Iteration " << (t + 1) << ": " << times[t] << " ms (sum: " << std::fixed << std::setprecision(2) << totalSum << ")" << std::endl;
    }
    
    return BenchmarkResult("Memory Allocation", times);
}

// Test 3: Cache-unfriendly Memory Access
BenchmarkResult cacheUnfriendlyAccess(int iterations, int matrixSize) {
    std::cout << "=== Cache-unfriendly Memory Access Benchmark ===" << std::endl;
    std::cout << "Matrix size: " << matrixSize << "x" << matrixSize << std::endl;
    
    std::vector<long long> times(iterations);
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 99);
    
    for (int t = 0; t < iterations; t++) {
        // Create large matrix
        std::vector<std::vector<int>> matrix(matrixSize, std::vector<int>(matrixSize));
        
        // Fill matrix
        for (int i = 0; i < matrixSize; i++) {
            for (int j = 0; j < matrixSize; j++) {
                matrix[i][j] = dis(gen);
            }
        }
        
        auto start = std::chrono::high_resolution_clock::now();
        
        // Column-wise access (cache-unfriendly)
        long long sum = 0;
        for (int j = 0; j < matrixSize; j++) {
            for (int i = 0; i < matrixSize; i++) {
                sum += matrix[i][j];
            }
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        times[t] = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
        std::cout << "Iteration " << (t + 1) << ": " << times[t] << " ms (sum: " << sum << ")" << std::endl;
    }
    
    return BenchmarkResult("Cache-unfriendly Access", times);
}

// Test 4: String Operations
BenchmarkResult stringOperations(int iterations, int stringCount) {
    std::cout << "=== String Operations Benchmark ===" << std::endl;
    std::cout << "String count: " << stringCount << std::endl;
    
    std::vector<long long> times(iterations);
    
    for (int t = 0; t < iterations; t++) {
        auto start = std::chrono::high_resolution_clock::now();
        
        std::vector<std::string> strings;
        strings.reserve(stringCount);
        std::string result;
        
        // Create many strings
        for (int i = 0; i < stringCount; i++) {
            std::string str = "String number " + std::to_string(i) + " with some additional text to make it longer";
            strings.push_back(str);
            result += str + " ";
        }
        
        // Process strings
        size_t totalLength = 0;
        for (const auto& str : strings) {
            totalLength += str.length();
            std::string upper = str;
            std::transform(upper.begin(), upper.end(), upper.begin(), ::toupper);
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        times[t] = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
        std::cout << "Iteration " << (t + 1) << ": " << times[t] << " ms (total length: " << totalLength << ", result length: " << result.length() << ")" << std::endl;
    }
    
    return BenchmarkResult("String Operations", times);
}

// Test 5: Collection Operations
BenchmarkResult collectionOperations(int iterations, int elementCount) {
    std::cout << "=== Collection Operations Benchmark ===" << std::endl;
    std::cout << "Element count: " << elementCount << std::endl;
    
    std::vector<long long> times(iterations);
    std::random_device rd;
    std::mt19937 gen(rd());
    
    for (int t = 0; t < iterations; t++) {
        auto start = std::chrono::high_resolution_clock::now();
        
        // Test different collections
        std::vector<int> vec;
        std::unordered_set<int> set;
        std::unordered_map<int, std::string> map;
        
        vec.reserve(elementCount);
        
        std::uniform_int_distribution<> dis(0, elementCount / 2);
        
        // Fill collections
        for (int i = 0; i < elementCount; i++) {
            int value = dis(gen); // Some duplicates
            vec.push_back(value);
            set.insert(value);
            map[value] = "Value " + std::to_string(value);
        }
        
        // Operations
        std::sort(vec.begin(), vec.end());
        
        long long sum = 0;
        for (int value : set) {
            sum += value;
        }
        
        for (const auto& entry : map) {
            entry.second.length(); // Access string
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        times[t] = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
        std::cout << "Iteration " << (t + 1) << ": " << times[t] << " ms (vec: " << vec.size() << ", set: " << set.size() << ", map: " << map.size() << ", sum: " << sum << ")" << std::endl;
    }
    
    return BenchmarkResult("Collection Operations", times);
}

int main() {
    std::cout << "C++ Memory-Bound Benchmark Suite" << std::endl;
    std::cout << "==================================" << std::endl;
    
    std::vector<BenchmarkResult> results;
    
    // Run benchmarks
    results.push_back(largeArrayOperations(10, 10000000));
    std::cout << std::endl;
    
    results.push_back(memoryAllocation(10, 10000));
    std::cout << std::endl;
    
    results.push_back(cacheUnfriendlyAccess(10, 2000));
    std::cout << std::endl;
    
    results.push_back(stringOperations(10, 100000));
    std::cout << std::endl;
    
    results.push_back(collectionOperations(10, 500000));
    std::cout << std::endl;
    
    // Print summary
    std::cout << "=== SUMMARY ===" << std::endl;
    for (const auto& result : results) {
        std::cout << std::left << std::setw(25) << result.testName << ": "
                  << "First: " << std::setw(6) << result.times[0] << " ms, "
                  << "Last: " << std::setw(6) << result.times[result.times.size() - 1] << " ms, "
                  << "Avg: " << std::setw(8) << std::fixed << std::setprecision(2) << result.averageTime << " ms, "
                  << "Improvement: " << std::setprecision(2) << result.improvementRatio << "x" << std::endl;
    }
    
    return 0;
}
