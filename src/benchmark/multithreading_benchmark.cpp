#include <iostream>
#include <vector>
#include <thread>
#include <chrono>
#include <random>
#include <algorithm>
#include <future>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <unordered_map>

struct ThreadingResult {
    std::string testName;
    int threadCount;
    long singleThreadTime;
    long multiThreadTime;
    double speedup;
    double efficiency;
    
    ThreadingResult(const std::string& name, int threads, long singleTime, long multiTime)
        : testName(name), threadCount(threads), singleThreadTime(singleTime), multiThreadTime(multiTime) {
        speedup = static_cast<double>(singleTime) / multiTime;
        efficiency = speedup / threads;
    }
};

// Test 1: Parallel Matrix Multiplication
ThreadingResult parallelMatrixMultiplication(int threadCount) {
    std::cout << "=== Parallel Matrix Multiplication Test ===" << std::endl;
    std::cout << "Thread count: " << threadCount << std::endl;
    
    const int matrixSize = 500;
    std::vector<std::vector<double>> A(matrixSize, std::vector<double>(matrixSize));
    std::vector<std::vector<double>> B(matrixSize, std::vector<double>(matrixSize));
    std::vector<std::vector<double>> C1(matrixSize, std::vector<double>(matrixSize, 0.0));
    std::vector<std::vector<double>> C2(matrixSize, std::vector<double>(matrixSize, 0.0));
    
    // Initialize matrices
    std::random_device rd;
    std::mt19937 gen(42);
    std::uniform_real_distribution<> dis(0.0, 1.0);
    
    for (int i = 0; i < matrixSize; i++) {
        for (int j = 0; j < matrixSize; j++) {
            A[i][j] = dis(gen);
            B[i][j] = dis(gen);
        }
    }
    
    // Single-threaded version
    auto startSingle = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < matrixSize; i++) {
        for (int k = 0; k < matrixSize; k++) {
            double aik = A[i][k];
            for (int j = 0; j < matrixSize; j++) {
                C1[i][j] += aik * B[k][j];
            }
        }
    }
    auto endSingle = std::chrono::high_resolution_clock::now();
    long singleThreadTime = std::chrono::duration_cast<std::chrono::milliseconds>(endSingle - startSingle).count();
    
    // Multi-threaded version
    auto startMulti = std::chrono::high_resolution_clock::now();
    std::vector<std::thread> threads;
    int rowsPerThread = matrixSize / threadCount;
    
    for (int t = 0; t < threadCount; t++) {
        int startRow = t * rowsPerThread;
        int endRow = (t == threadCount - 1) ? matrixSize : (t + 1) * rowsPerThread;
        
        threads.emplace_back([&, startRow, endRow]() {
            for (int i = startRow; i < endRow; i++) {
                for (int k = 0; k < matrixSize; k++) {
                    double aik = A[i][k];
                    for (int j = 0; j < matrixSize; j++) {
                        C2[i][j] += aik * B[k][j];
                    }
                }
            }
        });
    }
    
    for (auto& thread : threads) {
        thread.join();
    }
    auto endMulti = std::chrono::high_resolution_clock::now();
    long multiThreadTime = std::chrono::duration_cast<std::chrono::milliseconds>(endMulti - startMulti).count();
    
    std::cout << "Single-thread: " << singleThreadTime << " ms, Multi-thread: " << multiThreadTime 
              << " ms, Speedup: " << static_cast<double>(singleThreadTime) / multiThreadTime << "x" << std::endl;
    
    return ThreadingResult("Parallel Matrix Multiplication", threadCount, singleThreadTime, multiThreadTime);
}

// Test 2: Parallel Prime Calculation
bool isPrime(int n) {
    if (n < 2) return false;
    if (n == 2) return true;
    if (n % 2 == 0) return false;
    
    for (int i = 3; i * i <= n; i += 2) {
        if (n % i == 0) return false;
    }
    return true;
}

ThreadingResult parallelPrimeCalculation(int threadCount) {
    std::cout << "=== Parallel Prime Calculation Test ===" << std::endl;
    std::cout << "Thread count: " << threadCount << std::endl;
    
    const int maxNumber = 100000;
    
    // Single-threaded version
    auto startSingle = std::chrono::high_resolution_clock::now();
    std::vector<int> primesSingle;
    for (int num = 2; num <= maxNumber; num++) {
        if (isPrime(num)) {
            primesSingle.push_back(num);
        }
    }
    auto endSingle = std::chrono::high_resolution_clock::now();
    long singleThreadTime = std::chrono::duration_cast<std::chrono::milliseconds>(endSingle - startSingle).count();
    
    // Multi-threaded version
    auto startMulti = std::chrono::high_resolution_clock::now();
    std::vector<std::vector<int>> threadResults(threadCount);
    std::vector<std::thread> threads;
    
    int rangePerThread = maxNumber / threadCount;
    
    for (int t = 0; t < threadCount; t++) {
        int start = t * rangePerThread + 2;
        int end = (t == threadCount - 1) ? maxNumber : (t + 1) * rangePerThread + 1;
        
        threads.emplace_back([&, t, start, end]() {
            for (int num = start; num <= end; num++) {
                if (isPrime(num)) {
                    threadResults[t].push_back(num);
                }
            }
        });
    }
    
    for (auto& thread : threads) {
        thread.join();
    }
    
    // Combine results
    std::vector<int> primesMulti;
    for (const auto& result : threadResults) {
        primesMulti.insert(primesMulti.end(), result.begin(), result.end());
    }
    
    auto endMulti = std::chrono::high_resolution_clock::now();
    long multiThreadTime = std::chrono::duration_cast<std::chrono::milliseconds>(endMulti - startMulti).count();
    
    std::cout << "Single-thread: " << singleThreadTime << " ms (" << primesSingle.size() 
              << " primes), Multi-thread: " << multiThreadTime << " ms (" << primesMulti.size() 
              << " primes), Speedup: " << static_cast<double>(singleThreadTime) / multiThreadTime << "x" << std::endl;
    
    return ThreadingResult("Parallel Prime Calculation", threadCount, singleThreadTime, multiThreadTime);
}

// Test 3: Producer-Consumer Pattern
ThreadingResult producerConsumerTest(int threadCount) {
    std::cout << "=== Producer-Consumer Test ===" << std::endl;
    std::cout << "Thread count: " << threadCount << std::endl;
    
    const int itemCount = 1000000;
    
    // Single-threaded version
    auto startSingle = std::chrono::high_resolution_clock::now();
    std::vector<int> items;
    std::atomic<long long> sum(0);
    
    for (int i = 0; i < itemCount; i++) {
        items.push_back(i);
        sum += static_cast<long long>(i) * i;
    }
    auto endSingle = std::chrono::high_resolution_clock::now();
    long singleThreadTime = std::chrono::duration_cast<std::chrono::milliseconds>(endSingle - startSingle).count();
    
    // Multi-threaded version
    auto startMulti = std::chrono::high_resolution_clock::now();
    std::queue<int> queue;
    std::mutex queueMutex;
    std::condition_variable cv;
    std::atomic<long long> multiSum(0);
    std::atomic<bool> finished(false);
    
    // Producer thread
    std::thread producer([&]() {
        for (int i = 0; i < itemCount; i++) {
            {
                std::lock_guard<std::mutex> lock(queueMutex);
                queue.push(i);
            }
            cv.notify_one();
        }
        finished = true;
        cv.notify_all();
    });
    
    // Consumer threads
    std::vector<std::thread> consumers;
    for (int t = 0; t < threadCount - 1; t++) {
        consumers.emplace_back([&]() {
            while (true) {
                std::unique_lock<std::mutex> lock(queueMutex);
                cv.wait(lock, [&] { return !queue.empty() || finished; });
                
                if (queue.empty() && finished) break;
                
                if (!queue.empty()) {
                    int item = queue.front();
                    queue.pop();
                    lock.unlock();
                    
                    multiSum += static_cast<long long>(item) * item;
                }
            }
        });
    }
    
    producer.join();
    for (auto& consumer : consumers) {
        consumer.join();
    }
    
    auto endMulti = std::chrono::high_resolution_clock::now();
    long multiThreadTime = std::chrono::duration_cast<std::chrono::milliseconds>(endMulti - startMulti).count();
    
    std::cout << "Single-thread: " << singleThreadTime << " ms (sum: " << sum.load() 
              << "), Multi-thread: " << multiThreadTime << " ms (sum: " << multiSum.load() 
              << "), Speedup: " << static_cast<double>(singleThreadTime) / multiThreadTime << "x" << std::endl;
    
    return ThreadingResult("Producer-Consumer", threadCount, singleThreadTime, multiThreadTime);
}

// Test 4: Parallel Sorting
ThreadingResult parallelSorting(int threadCount) {
    std::cout << "=== Parallel Sorting Test ===" << std::endl;
    std::cout << "Thread count: " << threadCount << std::endl;
    
    const int arraySize = 10000000;
    
    // Generate random arrays
    std::random_device rd;
    std::mt19937 gen(42);
    std::uniform_int_distribution<> dis(0, arraySize - 1);
    
    std::vector<int> array1(arraySize);
    std::vector<int> array2(arraySize);
    
    for (int i = 0; i < arraySize; i++) {
        int value = dis(gen);
        array1[i] = value;
        array2[i] = value;
    }
    
    // Single-threaded sorting
    auto startSingle = std::chrono::high_resolution_clock::now();
    std::sort(array1.begin(), array1.end());
    auto endSingle = std::chrono::high_resolution_clock::now();
    long singleThreadTime = std::chrono::duration_cast<std::chrono::milliseconds>(endSingle - startSingle).count();
    
    // Multi-threaded sorting (merge sort approach)
    auto startMulti = std::chrono::high_resolution_clock::now();
    
    // Simple parallel sort using std::sort on chunks then merge
    int chunkSize = arraySize / threadCount;
    std::vector<std::thread> threads;
    
    for (int t = 0; t < threadCount; t++) {
        int start = t * chunkSize;
        int end = (t == threadCount - 1) ? arraySize : (t + 1) * chunkSize;
        
        threads.emplace_back([&, start, end]() {
            std::sort(array2.begin() + start, array2.begin() + end);
        });
    }
    
    for (auto& thread : threads) {
        thread.join();
    }
    
    // Merge sorted chunks (simplified)
    std::vector<int> temp(arraySize);
    std::copy(array2.begin(), array2.end(), temp.begin());
    std::sort(temp.begin(), temp.end()); // Final merge step
    
    auto endMulti = std::chrono::high_resolution_clock::now();
    long multiThreadTime = std::chrono::duration_cast<std::chrono::milliseconds>(endMulti - startMulti).count();
    
    std::cout << "Single-thread: " << singleThreadTime << " ms, Multi-thread: " << multiThreadTime 
              << " ms, Speedup: " << static_cast<double>(singleThreadTime) / multiThreadTime << "x" << std::endl;
    
    return ThreadingResult("Parallel Sorting", threadCount, singleThreadTime, multiThreadTime);
}

int main() {
    std::cout << "C++ Multithreading Benchmark Suite" << std::endl;
    std::cout << "===================================" << std::endl;
    
    int threadCount = std::max(4, static_cast<int>(std::thread::hardware_concurrency()));
    
    std::cout << "Hardware concurrency: " << std::thread::hardware_concurrency() << std::endl;
    std::cout << "Using thread count: " << threadCount << std::endl;
    std::cout << std::endl;
    
    std::vector<ThreadingResult> results;
    
    // Run multithreading benchmarks
    results.push_back(parallelMatrixMultiplication(threadCount));
    std::cout << std::endl;
    
    results.push_back(parallelPrimeCalculation(threadCount));
    std::cout << std::endl;
    
    results.push_back(producerConsumerTest(threadCount));
    std::cout << std::endl;
    
    results.push_back(parallelSorting(threadCount));
    std::cout << std::endl;
    
    // Print threading performance summary
    std::cout << "=== MULTITHREADING PERFORMANCE SUMMARY ===" << std::endl;
    std::cout << std::left << std::setw(30) << "Test" 
              << std::setw(12) << "Single(ms)" 
              << std::setw(12) << "Multi(ms)" 
              << std::setw(12) << "Speedup" 
              << std::setw(12) << "Efficiency" << std::endl;
    std::cout << std::string(78, '-') << std::endl;
    
    for (const auto& result : results) {
        std::cout << std::left << std::setw(30) << result.testName
                  << std::setw(12) << result.singleThreadTime
                  << std::setw(12) << result.multiThreadTime
                  << std::setw(12) << std::fixed << std::setprecision(2) << result.speedup << "x"
                  << std::setw(12) << std::setprecision(2) << (result.efficiency * 100) << "%" << std::endl;
    }
    
    std::cout << std::endl;
    double avgSpeedup = 0;
    double avgEfficiency = 0;
    for (const auto& result : results) {
        avgSpeedup += result.speedup;
        avgEfficiency += result.efficiency;
    }
    avgSpeedup /= results.size();
    avgEfficiency /= results.size();
    
    std::cout << "Average Speedup: " << std::fixed << std::setprecision(2) << avgSpeedup << "x" << std::endl;
    std::cout << "Average Efficiency: " << std::setprecision(2) << (avgEfficiency * 100) << "%" << std::endl;
    
    return 0;
}
