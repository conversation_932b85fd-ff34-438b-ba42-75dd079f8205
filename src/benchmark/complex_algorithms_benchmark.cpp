#include <iostream>
#include <vector>
#include <chrono>
#include <random>
#include <algorithm>
#include <queue>
#include <string>
#include <climits>
#include <iomanip>

class BenchmarkResult {
public:
    std::string testName;
    std::vector<long long> times;
    double averageTime;
    double improvementRatio;
    
    BenchmarkResult(const std::string& name, const std::vector<long long>& t) 
        : testName(name), times(t) {
        calculateStats();
    }
    
private:
    void calculateStats() {
        long long sum = 0;
        for (long long time : times) sum += time;
        averageTime = (double)sum / times.size();
        improvementRatio = times.size() > 1 ? (double)times[0] / times[times.size() - 1] : 1.0;
    }
};

// Test 1: QuickSort Algorithm
BenchmarkResult quickSortBenchmark(int iterations, int arraySize) {
    std::cout << "=== QuickSort Benchmark ===" << std::endl;
    std::cout << "Array size: " << arraySize << std::endl;
    
    std::vector<long long> times(iterations);
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, arraySize - 1);
    
    auto quickSort = [](std::vector<int>& arr, int low, int high) -> void {
        std::function<void(std::vector<int>&, int, int)> quickSortRec = 
            [&](std::vector<int>& arr, int low, int high) -> void {
            if (low < high) {
                auto partition = [](std::vector<int>& arr, int low, int high) -> int {
                    int pivot = arr[high];
                    int i = low - 1;
                    
                    for (int j = low; j < high; j++) {
                        if (arr[j] <= pivot) {
                            i++;
                            std::swap(arr[i], arr[j]);
                        }
                    }
                    std::swap(arr[i + 1], arr[high]);
                    return i + 1;
                };
                
                int pi = partition(arr, low, high);
                quickSortRec(arr, low, pi - 1);
                quickSortRec(arr, pi + 1, high);
            }
        };
        quickSortRec(arr, low, high);
    };
    
    for (int t = 0; t < iterations; t++) {
        // Generate random array
        std::vector<int> array(arraySize);
        for (int i = 0; i < arraySize; i++) {
            array[i] = dis(gen);
        }
        
        auto start = std::chrono::high_resolution_clock::now();
        quickSort(array, 0, array.size() - 1);
        auto end = std::chrono::high_resolution_clock::now();
        
        times[t] = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
        std::cout << "Iteration " << (t + 1) << ": " << times[t] << " ms (first: " << array[0] << ", last: " << array[array.size() - 1] << ")" << std::endl;
    }
    
    return BenchmarkResult("QuickSort", times);
}

// Test 2: Binary Search Tree Operations
class BinarySearchTree {
    struct Node {
        int data;
        Node* left;
        Node* right;
        
        Node(int val) : data(val), left(nullptr), right(nullptr) {}
    };
    
    Node* root;
    
    Node* insertRec(Node* root, int data) {
        if (root == nullptr) {
            return new Node(data);
        }
        
        if (data < root->data) {
            root->left = insertRec(root->left, data);
        } else if (data > root->data) {
            root->right = insertRec(root->right, data);
        }
        
        return root;
    }
    
    bool searchRec(Node* root, int data) {
        if (root == nullptr) return false;
        if (root->data == data) return true;
        
        return data < root->data ? searchRec(root->left, data) : searchRec(root->right, data);
    }
    
    void inorderRec(Node* root, std::vector<int>& result) {
        if (root != nullptr) {
            inorderRec(root->left, result);
            result.push_back(root->data);
            inorderRec(root->right, result);
        }
    }
    
    void deleteTree(Node* root) {
        if (root != nullptr) {
            deleteTree(root->left);
            deleteTree(root->right);
            delete root;
        }
    }
    
public:
    BinarySearchTree() : root(nullptr) {}
    
    ~BinarySearchTree() {
        deleteTree(root);
    }
    
    void insert(int data) {
        root = insertRec(root, data);
    }
    
    bool search(int data) {
        return searchRec(root, data);
    }
    
    std::vector<int> inorderTraversal() {
        std::vector<int> result;
        inorderRec(root, result);
        return result;
    }
};

BenchmarkResult binarySearchTreeBenchmark(int iterations, int nodeCount) {
    std::cout << "=== Binary Search Tree Benchmark ===" << std::endl;
    std::cout << "Node count: " << nodeCount << std::endl;
    
    std::vector<long long> times(iterations);
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, nodeCount * 2 - 1);
    
    for (int t = 0; t < iterations; t++) {
        auto start = std::chrono::high_resolution_clock::now();
        
        BinarySearchTree bst;
        
        // Insert nodes
        for (int i = 0; i < nodeCount; i++) {
            bst.insert(dis(gen));
        }
        
        // Search operations
        int foundCount = 0;
        for (int i = 0; i < nodeCount / 10; i++) {
            if (bst.search(dis(gen))) {
                foundCount++;
            }
        }
        
        // Tree traversal
        std::vector<int> inorderResult = bst.inorderTraversal();
        
        auto end = std::chrono::high_resolution_clock::now();
        times[t] = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
        std::cout << "Iteration " << (t + 1) << ": " << times[t] << " ms (found: " << foundCount << ", traversal size: " << inorderResult.size() << ")" << std::endl;
    }
    
    return BenchmarkResult("Binary Search Tree", times);
}

// Test 3: Graph Algorithms (Dijkstra's shortest path)
class Graph {
    struct Edge {
        int destination;
        int weight;
        
        Edge(int dest, int w) : destination(dest), weight(w) {}
    };
    
    int vertices;
    std::vector<std::vector<Edge>> adjacencyList;
    
public:
    Graph(int v) : vertices(v) {
        adjacencyList.resize(v);
    }
    
    void addEdge(int source, int destination, int weight) {
        adjacencyList[source].emplace_back(destination, weight);
    }
    
    std::vector<int> dijkstra(int source) {
        std::vector<int> distances(vertices, INT_MAX);
        distances[source] = 0;
        
        std::priority_queue<std::pair<int, int>, std::vector<std::pair<int, int>>, std::greater<std::pair<int, int>>> pq;
        pq.push({0, source});
        
        while (!pq.empty()) {
            int u = pq.top().second;
            pq.pop();
            
            for (const Edge& edge : adjacencyList[u]) {
                int v = edge.destination;
                int weight = edge.weight;
                
                if (distances[u] != INT_MAX && distances[u] + weight < distances[v]) {
                    distances[v] = distances[u] + weight;
                    pq.push({distances[v], v});
                }
            }
        }
        
        return distances;
    }
};

BenchmarkResult dijkstraAlgorithm(int iterations, int nodeCount) {
    std::cout << "=== Dijkstra's Algorithm Benchmark ===" << std::endl;
    std::cout << "Node count: " << nodeCount << std::endl;
    
    std::vector<long long> times(iterations);
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> nodeDis(0, nodeCount - 1);
    std::uniform_int_distribution<> weightDis(1, 100);
    
    for (int t = 0; t < iterations; t++) {
        auto start = std::chrono::high_resolution_clock::now();
        
        // Create graph
        Graph graph(nodeCount);
        
        // Add random edges
        int edgeCount = nodeCount * 3; // Dense graph
        for (int i = 0; i < edgeCount; i++) {
            int from = nodeDis(gen);
            int to = nodeDis(gen);
            int weight = weightDis(gen);
            graph.addEdge(from, to, weight);
        }
        
        // Run Dijkstra from source 0
        std::vector<int> distances = graph.dijkstra(0);
        
        // Count reachable nodes
        int reachableCount = 0;
        for (int dist : distances) {
            if (dist != INT_MAX) {
                reachableCount++;
            }
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        times[t] = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
        std::cout << "Iteration " << (t + 1) << ": " << times[t] << " ms (reachable nodes: " << reachableCount << ")" << std::endl;
    }
    
    return BenchmarkResult("Dijkstra Algorithm", times);
}

// Test 4: Dynamic Programming (Longest Common Subsequence)
int lcs(const std::string& str1, const std::string& str2) {
    int m = str1.length();
    int n = str2.length();
    std::vector<std::vector<int>> dp(m + 1, std::vector<int>(n + 1, 0));
    
    for (int i = 1; i <= m; i++) {
        for (int j = 1; j <= n; j++) {
            if (str1[i - 1] == str2[j - 1]) {
                dp[i][j] = dp[i - 1][j - 1] + 1;
            } else {
                dp[i][j] = std::max(dp[i - 1][j], dp[i][j - 1]);
            }
        }
    }
    
    return dp[m][n];
}

BenchmarkResult longestCommonSubsequence(int iterations, int stringLength) {
    std::cout << "=== Longest Common Subsequence Benchmark ===" << std::endl;
    std::cout << "String length: " << stringLength << std::endl;
    
    std::vector<long long> times(iterations);
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> charDis(0, 3); // A, B, C, D
    
    for (int t = 0; t < iterations; t++) {
        // Generate random strings
        std::string str1, str2;
        str1.reserve(stringLength);
        str2.reserve(stringLength);
        
        for (int i = 0; i < stringLength; i++) {
            str1 += static_cast<char>('A' + charDis(gen));
            str2 += static_cast<char>('A' + charDis(gen));
        }
        
        auto start = std::chrono::high_resolution_clock::now();
        int lcsLength = lcs(str1, str2);
        auto end = std::chrono::high_resolution_clock::now();
        
        times[t] = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
        std::cout << "Iteration " << (t + 1) << ": " << times[t] << " ms (LCS length: " << lcsLength << ")" << std::endl;
    }
    
    return BenchmarkResult("Longest Common Subsequence", times);
}

int main() {
    std::cout << "C++ Complex Algorithms Benchmark Suite" << std::endl;
    std::cout << "=======================================" << std::endl;
    
    std::vector<BenchmarkResult> results;
    
    // Run benchmarks
    results.push_back(quickSortBenchmark(10, 100000));
    std::cout << std::endl;
    
    results.push_back(binarySearchTreeBenchmark(10, 50000));
    std::cout << std::endl;
    
    results.push_back(dijkstraAlgorithm(10, 1000));
    std::cout << std::endl;
    
    results.push_back(longestCommonSubsequence(10, 500));
    std::cout << std::endl;
    
    // Print summary
    std::cout << "=== SUMMARY ===" << std::endl;
    for (const auto& result : results) {
        std::cout << std::left << std::setw(30) << result.testName << ": "
                  << "First: " << std::setw(6) << result.times[0] << " ms, "
                  << "Last: " << std::setw(6) << result.times[result.times.size() - 1] << " ms, "
                  << "Avg: " << std::setw(8) << std::fixed << std::setprecision(2) << result.averageTime << " ms, "
                  << "Improvement: " << std::setprecision(2) << result.improvementRatio << "x" << std::endl;
    }
    
    return 0;
}
