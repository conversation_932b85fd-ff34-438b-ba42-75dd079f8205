#!/usr/bin/env python3
"""
Python3 Complex Algorithms Benchmark Suite
"""

import time
import random
import heapq
from typing import List, Dict, Tuple, Optional

class BenchmarkResult:
    def __init__(self, test_name: str, times: List[float]):
        self.test_name = test_name
        self.times = times
        self.average_time = sum(times) / len(times)
        self.improvement_ratio = times[0] / times[-1] if len(times) > 1 and times[-1] > 0 else 1.0

def quicksort_benchmark(iterations: int, array_size: int) -> BenchmarkResult:
    """QuickSort algorithm benchmark"""
    print("=== QuickSort Benchmark ===")
    print(f"Array size: {array_size}")
    
    times = []
    
    def quicksort(arr: List[int], low: int, high: int):
        if low < high:
            pi = partition(arr, low, high)
            quicksort(arr, low, pi - 1)
            quicksort(arr, pi + 1, high)
    
    def partition(arr: List[int], low: int, high: int) -> int:
        pivot = arr[high]
        i = low - 1
        
        for j in range(low, high):
            if arr[j] <= pivot:
                i += 1
                arr[i], arr[j] = arr[j], arr[i]
        
        arr[i + 1], arr[high] = arr[high], arr[i + 1]
        return i + 1
    
    for t in range(iterations):
        # Generate random array
        array = [random.randint(0, array_size - 1) for _ in range(array_size)]
        
        start_time = time.perf_counter()
        quicksort(array, 0, len(array) - 1)
        end_time = time.perf_counter()
        
        elapsed_ms = (end_time - start_time) * 1000
        times.append(elapsed_ms)
        print(f"Iteration {t + 1}: {elapsed_ms:.0f} ms (first: {array[0]}, last: {array[-1]})")
    
    return BenchmarkResult("QuickSort", times)

class BinarySearchTree:
    class Node:
        def __init__(self, data: int):
            self.data = data
            self.left: Optional['BinarySearchTree.Node'] = None
            self.right: Optional['BinarySearchTree.Node'] = None
    
    def __init__(self):
        self.root: Optional[BinarySearchTree.Node] = None
    
    def insert(self, data: int):
        self.root = self._insert_rec(self.root, data)
    
    def _insert_rec(self, root: Optional['BinarySearchTree.Node'], data: int) -> 'BinarySearchTree.Node':
        if root is None:
            return BinarySearchTree.Node(data)
        
        if data < root.data:
            root.left = self._insert_rec(root.left, data)
        elif data > root.data:
            root.right = self._insert_rec(root.right, data)
        
        return root
    
    def search(self, data: int) -> bool:
        return self._search_rec(self.root, data)
    
    def _search_rec(self, root: Optional['BinarySearchTree.Node'], data: int) -> bool:
        if root is None:
            return False
        if root.data == data:
            return True
        
        return self._search_rec(root.left, data) if data < root.data else self._search_rec(root.right, data)
    
    def inorder_traversal(self) -> List[int]:
        result = []
        self._inorder_rec(self.root, result)
        return result
    
    def _inorder_rec(self, root: Optional['BinarySearchTree.Node'], result: List[int]):
        if root is not None:
            self._inorder_rec(root.left, result)
            result.append(root.data)
            self._inorder_rec(root.right, result)

def binary_search_tree_benchmark(iterations: int, node_count: int) -> BenchmarkResult:
    """Binary Search Tree operations benchmark"""
    print("=== Binary Search Tree Benchmark ===")
    print(f"Node count: {node_count}")
    
    times = []
    
    for t in range(iterations):
        start_time = time.perf_counter()
        
        bst = BinarySearchTree()
        
        # Insert nodes
        for _ in range(node_count):
            bst.insert(random.randint(0, node_count * 2 - 1))
        
        # Search operations
        found_count = 0
        for _ in range(node_count // 10):
            if bst.search(random.randint(0, node_count * 2 - 1)):
                found_count += 1
        
        # Tree traversal
        inorder_result = bst.inorder_traversal()
        
        end_time = time.perf_counter()
        elapsed_ms = (end_time - start_time) * 1000
        times.append(elapsed_ms)
        print(f"Iteration {t + 1}: {elapsed_ms:.0f} ms (found: {found_count}, traversal size: {len(inorder_result)})")
    
    return BenchmarkResult("Binary Search Tree", times)

class Graph:
    def __init__(self, vertices: int):
        self.vertices = vertices
        self.adjacency_list: List[List[Tuple[int, int]]] = [[] for _ in range(vertices)]
    
    def add_edge(self, source: int, destination: int, weight: int):
        self.adjacency_list[source].append((destination, weight))
    
    def dijkstra(self, source: int) -> List[int]:
        distances = [float('inf')] * self.vertices
        distances[source] = 0
        
        pq = [(0, source)]
        
        while pq:
            current_dist, u = heapq.heappop(pq)
            
            if current_dist > distances[u]:
                continue
            
            for v, weight in self.adjacency_list[u]:
                distance = distances[u] + weight
                
                if distance < distances[v]:
                    distances[v] = distance
                    heapq.heappush(pq, (distance, v))
        
        return [d if d != float('inf') else -1 for d in distances]

def dijkstra_algorithm(iterations: int, node_count: int) -> BenchmarkResult:
    """Dijkstra's shortest path algorithm benchmark"""
    print("=== Dijkstra's Algorithm Benchmark ===")
    print(f"Node count: {node_count}")
    
    times = []
    
    for t in range(iterations):
        start_time = time.perf_counter()
        
        # Create graph
        graph = Graph(node_count)
        
        # Add random edges
        edge_count = node_count * 3  # Dense graph
        for _ in range(edge_count):
            from_node = random.randint(0, node_count - 1)
            to_node = random.randint(0, node_count - 1)
            weight = random.randint(1, 100)
            graph.add_edge(from_node, to_node, weight)
        
        # Run Dijkstra from source 0
        distances = graph.dijkstra(0)
        
        # Count reachable nodes
        reachable_count = sum(1 for d in distances if d != -1)
        
        end_time = time.perf_counter()
        elapsed_ms = (end_time - start_time) * 1000
        times.append(elapsed_ms)
        print(f"Iteration {t + 1}: {elapsed_ms:.0f} ms (reachable nodes: {reachable_count})")
    
    return BenchmarkResult("Dijkstra Algorithm", times)

def longest_common_subsequence(iterations: int, string_length: int) -> BenchmarkResult:
    """Longest Common Subsequence dynamic programming benchmark"""
    print("=== Longest Common Subsequence Benchmark ===")
    print(f"String length: {string_length}")
    
    times = []
    
    def lcs(str1: str, str2: str) -> int:
        m, n = len(str1), len(str2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if str1[i - 1] == str2[j - 1]:
                    dp[i][j] = dp[i - 1][j - 1] + 1
                else:
                    dp[i][j] = max(dp[i - 1][j], dp[i][j - 1])
        
        return dp[m][n]
    
    for t in range(iterations):
        # Generate random strings
        str1 = ''.join(random.choice('ABCD') for _ in range(string_length))
        str2 = ''.join(random.choice('ABCD') for _ in range(string_length))
        
        start_time = time.perf_counter()
        lcs_length = lcs(str1, str2)
        end_time = time.perf_counter()
        
        elapsed_ms = (end_time - start_time) * 1000
        times.append(elapsed_ms)
        print(f"Iteration {t + 1}: {elapsed_ms:.0f} ms (LCS length: {lcs_length})")
    
    return BenchmarkResult("Longest Common Subsequence", times)

def main():
    print("Python3 Complex Algorithms Benchmark Suite")
    print("===========================================")
    
    results = []
    
    # Run benchmarks
    results.append(quicksort_benchmark(10, 100000))
    print()
    
    results.append(binary_search_tree_benchmark(10, 50000))
    print()
    
    results.append(dijkstra_algorithm(10, 1000))
    print()
    
    results.append(longest_common_subsequence(10, 500))
    print()
    
    # Print summary
    print("=== SUMMARY ===")
    for result in results:
        print(f"{result.test_name:<30}: First: {result.times[0]:6.0f} ms, "
              f"Last: {result.times[-1]:6.0f} ms, "
              f"Avg: {result.average_time:8.2f} ms, "
              f"Improvement: {result.improvement_ratio:.2f}x")

if __name__ == "__main__":
    main()
