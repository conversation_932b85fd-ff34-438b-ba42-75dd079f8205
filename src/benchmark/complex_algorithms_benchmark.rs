use rand::Rng;
use std::collections::BinaryHeap;
use std::cmp::Reverse;
use std::time::Instant;

struct BenchmarkResult {
    test_name: String,
    times: Vec<u128>,
    average_time: f64,
    improvement_ratio: f64,
}

impl BenchmarkResult {
    fn new(test_name: String, times: Vec<u128>) -> Self {
        let average_time = times.iter().sum::<u128>() as f64 / times.len() as f64;
        let improvement_ratio = if times.len() > 1 && times[times.len() - 1] > 0 {
            times[0] as f64 / times[times.len() - 1] as f64
        } else {
            1.0
        };
        
        BenchmarkResult {
            test_name,
            times,
            average_time,
            improvement_ratio,
        }
    }
}

fn quicksort_benchmark(iterations: usize, array_size: usize) -> BenchmarkResult {
    println!("=== QuickSort Benchmark ===");
    println!("Array size: {}", array_size);
    
    let mut times = Vec::new();
    let mut rng = rand::thread_rng();
    
    fn quicksort(arr: &mut [i32], low: isize, high: isize) {
        if low < high {
            let pi = partition(arr, low, high);
            quicksort(arr, low, pi - 1);
            quicksort(arr, pi + 1, high);
        }
    }
    
    fn partition(arr: &mut [i32], low: isize, high: isize) -> isize {
        let pivot = arr[high as usize];
        let mut i = low - 1;
        
        for j in low..high {
            if arr[j as usize] <= pivot {
                i += 1;
                arr.swap(i as usize, j as usize);
            }
        }
        arr.swap((i + 1) as usize, high as usize);
        i + 1
    }
    
    for t in 0..iterations {
        // Generate random array
        let mut array: Vec<i32> = (0..array_size).map(|_| rng.gen_range(0..array_size as i32)).collect();
        
        let array_len = array.len();
        let start = Instant::now();
        quicksort(&mut array, 0, (array_len - 1) as isize);
        let elapsed = start.elapsed().as_millis();
        
        times.push(elapsed);
        println!("Iteration {}: {} ms (first: {}, last: {})", t + 1, elapsed, array[0], array[array.len() - 1]);
    }
    
    BenchmarkResult::new("QuickSort".to_string(), times)
}

#[derive(Debug)]
struct BinarySearchTree {
    root: Option<Box<Node>>,
}

#[derive(Debug)]
struct Node {
    data: i32,
    left: Option<Box<Node>>,
    right: Option<Box<Node>>,
}

impl BinarySearchTree {
    fn new() -> Self {
        BinarySearchTree { root: None }
    }
    
    fn insert(&mut self, data: i32) {
        self.root = Self::insert_rec(self.root.take(), data);
    }
    
    fn insert_rec(root: Option<Box<Node>>, data: i32) -> Option<Box<Node>> {
        match root {
            None => Some(Box::new(Node { data, left: None, right: None })),
            Some(mut node) => {
                if data < node.data {
                    node.left = Self::insert_rec(node.left.take(), data);
                } else if data > node.data {
                    node.right = Self::insert_rec(node.right.take(), data);
                }
                Some(node)
            }
        }
    }
    
    fn search(&self, data: i32) -> bool {
        Self::search_rec(&self.root, data)
    }
    
    fn search_rec(root: &Option<Box<Node>>, data: i32) -> bool {
        match root {
            None => false,
            Some(node) => {
                if node.data == data {
                    true
                } else if data < node.data {
                    Self::search_rec(&node.left, data)
                } else {
                    Self::search_rec(&node.right, data)
                }
            }
        }
    }
    
    fn inorder_traversal(&self) -> Vec<i32> {
        let mut result = Vec::new();
        Self::inorder_rec(&self.root, &mut result);
        result
    }
    
    fn inorder_rec(root: &Option<Box<Node>>, result: &mut Vec<i32>) {
        if let Some(node) = root {
            Self::inorder_rec(&node.left, result);
            result.push(node.data);
            Self::inorder_rec(&node.right, result);
        }
    }
}

fn binary_search_tree_benchmark(iterations: usize, node_count: usize) -> BenchmarkResult {
    println!("=== Binary Search Tree Benchmark ===");
    println!("Node count: {}", node_count);
    
    let mut times = Vec::new();
    let mut rng = rand::thread_rng();
    
    for t in 0..iterations {
        let start = Instant::now();
        
        let mut bst = BinarySearchTree::new();
        
        // Insert nodes
        for _ in 0..node_count {
            bst.insert(rng.gen_range(0..(node_count * 2) as i32));
        }
        
        // Search operations
        let mut found_count = 0;
        for _ in 0..(node_count / 10) {
            if bst.search(rng.gen_range(0..(node_count * 2) as i32)) {
                found_count += 1;
            }
        }
        
        // Tree traversal
        let inorder_result = bst.inorder_traversal();
        
        let elapsed = start.elapsed().as_millis();
        times.push(elapsed);
        println!("Iteration {}: {} ms (found: {}, traversal size: {})", t + 1, elapsed, found_count, inorder_result.len());
    }
    
    BenchmarkResult::new("Binary Search Tree".to_string(), times)
}

struct Graph {
    vertices: usize,
    adjacency_list: Vec<Vec<(usize, i32)>>,
}

impl Graph {
    fn new(vertices: usize) -> Self {
        Graph {
            vertices,
            adjacency_list: vec![Vec::new(); vertices],
        }
    }
    
    fn add_edge(&mut self, source: usize, destination: usize, weight: i32) {
        self.adjacency_list[source].push((destination, weight));
    }
    
    fn dijkstra(&self, source: usize) -> Vec<i32> {
        let mut distances = vec![i32::MAX; self.vertices];
        distances[source] = 0;
        
        let mut pq = BinaryHeap::new();
        pq.push(Reverse((0, source)));
        
        while let Some(Reverse((current_dist, u))) = pq.pop() {
            if current_dist > distances[u] {
                continue;
            }
            
            for &(v, weight) in &self.adjacency_list[u] {
                let distance = distances[u].saturating_add(weight);
                
                if distance < distances[v] {
                    distances[v] = distance;
                    pq.push(Reverse((distance, v)));
                }
            }
        }
        
        distances
    }
}

fn dijkstra_algorithm(iterations: usize, node_count: usize) -> BenchmarkResult {
    println!("=== Dijkstra's Algorithm Benchmark ===");
    println!("Node count: {}", node_count);
    
    let mut times = Vec::new();
    let mut rng = rand::thread_rng();
    
    for t in 0..iterations {
        let start = Instant::now();
        
        // Create graph
        let mut graph = Graph::new(node_count);
        
        // Add random edges
        let edge_count = node_count * 3; // Dense graph
        for _ in 0..edge_count {
            let from = rng.gen_range(0..node_count);
            let to = rng.gen_range(0..node_count);
            let weight = rng.gen_range(1..101);
            graph.add_edge(from, to, weight);
        }
        
        // Run Dijkstra from source 0
        let distances = graph.dijkstra(0);
        
        // Count reachable nodes
        let reachable_count = distances.iter().filter(|&&d| d != i32::MAX).count();
        
        let elapsed = start.elapsed().as_millis();
        times.push(elapsed);
        println!("Iteration {}: {} ms (reachable nodes: {})", t + 1, elapsed, reachable_count);
    }
    
    BenchmarkResult::new("Dijkstra Algorithm".to_string(), times)
}

fn longest_common_subsequence(iterations: usize, string_length: usize) -> BenchmarkResult {
    println!("=== Longest Common Subsequence Benchmark ===");
    println!("String length: {}", string_length);
    
    let mut times = Vec::new();
    let mut rng = rand::thread_rng();
    
    fn lcs(str1: &str, str2: &str) -> usize {
        let chars1: Vec<char> = str1.chars().collect();
        let chars2: Vec<char> = str2.chars().collect();
        let m = chars1.len();
        let n = chars2.len();
        
        let mut dp = vec![vec![0; n + 1]; m + 1];
        
        for i in 1..=m {
            for j in 1..=n {
                if chars1[i - 1] == chars2[j - 1] {
                    dp[i][j] = dp[i - 1][j - 1] + 1;
                } else {
                    dp[i][j] = dp[i - 1][j].max(dp[i][j - 1]);
                }
            }
        }
        
        dp[m][n]
    }
    
    for t in 0..iterations {
        // Generate random strings
        let chars = ['A', 'B', 'C', 'D'];
        let str1: String = (0..string_length).map(|_| chars[rng.gen_range(0..4)]).collect();
        let str2: String = (0..string_length).map(|_| chars[rng.gen_range(0..4)]).collect();
        
        let start = Instant::now();
        let lcs_length = lcs(&str1, &str2);
        let elapsed = start.elapsed().as_millis();
        
        times.push(elapsed);
        println!("Iteration {}: {} ms (LCS length: {})", t + 1, elapsed, lcs_length);
    }
    
    BenchmarkResult::new("Longest Common Subsequence".to_string(), times)
}

fn main() {
    println!("Rust Complex Algorithms Benchmark Suite");
    println!("========================================");
    
    let mut results = Vec::new();
    
    // Run benchmarks
    results.push(quicksort_benchmark(10, 100000));
    println!();
    
    results.push(binary_search_tree_benchmark(10, 50000));
    println!();
    
    results.push(dijkstra_algorithm(10, 1000));
    println!();
    
    results.push(longest_common_subsequence(10, 500));
    println!();
    
    // Print summary
    println!("=== SUMMARY ===");
    for result in &results {
        println!("{:<30}: First: {:6} ms, Last: {:6} ms, Avg: {:8.2} ms, Improvement: {:.2}x",
            result.test_name,
            result.times[0],
            result.times[result.times.len() - 1],
            result.average_time,
            result.improvement_ratio
        );
    }
}
